---
import WidgetWrapper from '~/components/ui/WidgetWrapper.astro';
import Grid from '~/components/latin-events/Grid.astro';
import type { Widget } from '~/types';
import { latinEvents } from '~/connectors/airtable';
import Button from '~/components/ui/Button.astro';

export interface Props extends Widget {
  title?: string;
  linkText?: string;
  linkUrl?: string | URL;
  information?: string;
  count?: number;
  officials?: boolean;
}

const {
  title = await Astro.slots.render('title'),
  linkText,
  // linkUrl = getPermalink('events'),
  linkUrl,
  information = await Astro.slots.render('information'),
  count = 4,
  officials = false,

  id,
  isDark = false,
  classes = {},
  bg = await Astro.slots.render('bg'),
} = Astro.props;

let latinEventsFiltered = [...latinEvents];

if (officials) latinEventsFiltered = latinEventsFiltered.filter((e) => e.flyer);
---

<WidgetWrapper id={id} isDark={isDark} containerClass={classes?.container} bg={bg}>
  <div class="flex flex-col lg:justify-between lg:flex-row mb-8">
    {
      title && (
        <div class="md:max-w-sm">
          <h2
            class="text-3xl font-bold tracking-tight sm:text-4xl sm:leading-none group font-heading mb-2"
            set:html={title}
          />
          {linkText && (
            <Button variant="link" href={linkUrl || ''}>
              {' '}
              {linkText}
            </Button>
          )}
        </div>
      )
    }

    {information && <p class="text-muted dark:text-slate-400 lg:text-sm lg:max-w-md" set:html={information} />}
  </div>

  <Grid events={latinEventsFiltered} />
</WidgetWrapper>
