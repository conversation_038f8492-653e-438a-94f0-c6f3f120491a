import { ui, locales, defaultLang, showDefaultLang, routes } from './ui';

type Lang = keyof typeof ui;

export function getLangFromUrl(url: URL): Lang {
    const [, lang] = url.pathname.split('/');
    return lang in ui ? lang as Lang : defaultLang;
}

export function useTranslations(lang: <PERSON>) {
    return function t(key: keyof typeof ui[typeof defaultLang]) {
        return ui[lang][key] || ui[defaultLang][key];
    }
}

export function useTranslatedPath(lang: Lang) {
    return function translatePath(path: string, l: Lang = lang): string {
        const pathSegments = path.split('/').filter(Boolean);
        let translatedSegments: string[];

        if (l === defaultLang) {
            const currentRouteLang = locales.includes(pathSegments[0] as Lang) ? pathSegments.shift() as Lang : l;
            translatedSegments = pathSegments.map(segment =>
                getKeyByValue(routes[currentRouteLang], segment) ?? segment
            );
        } else {
            translatedSegments = pathSegments.map(segment =>
                routes[l]?.[segment] ?? segment
            );
        }

        const translatedPath = '/' + translatedSegments.join('/');
        return !showDefaultLang && l === defaultLang ? translatedPath : `/${l}${translatedPath}`;
    };
}

function getKeyByValue<T extends Record<string, string>>(obj: T, value: string): keyof T | undefined {
    return Object.keys(obj).find(key => obj[key] === value) as keyof T | undefined;
}

// Questa funzione non è più utilizzata, ma l'ho mantenuta e migliorata per riferimento futuro
export function getRouteFromUrl(url: URL): string | undefined {
    const parts = new URL(url).pathname.split('/').filter(Boolean);
    const path = parts.pop() || '';
    const currentLang = getLangFromUrl(url);

    if (currentLang === defaultLang) {
        return Object.values(routes)[0][path];
    }

    return getKeyByValue(routes[currentLang], path);
}