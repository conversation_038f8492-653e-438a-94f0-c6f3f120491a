---
import Layout from '~/layouts/PageLayout.astro';
import CallToAction from '~/components/widgets/CallToAction.astro';
import Stats from '~/components/widgets/Stats.astro';
import SocializationHero from '~/components/dancing/SocializationHero.astro';
import SocializationPastEventsCta from '~/components/socialization/SocializationPastEventsCta.astro';
import FollowSectionSocials from '~/components/common/FollowSectionSocials.astro';

import { getLangFromUrl, useTranslations, useTranslatedPath } from '~/i18n/utils';
import EventsHorizontalSwiper from '~/components/widgets/EventsHorizontalSwiper.astro';

const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);
const translatePath = useTranslatedPath(lang);

const { Content } = await import(`~/content/standalone/${lang}/socialization.mdx`);

const metadata = {
  title: t('socializationPage.metadata.title'),
  description: t('socializationPage.metadata.description'),
};
---

<Layout metadata={metadata}>
  <SocializationHero />

  <section class="px-4 py-16 sm:px-6 mx-auto lg:px-8 lg:py-20 max-w-4xl">
    <div class="mx-auto prose prose-lg max-w-none dark:prose-invert">
      <Content />
    </div>
  </section>

  <EventsHorizontalSwiper
    id="social-events"
    title={t('indexPage.latestSocialEvents.title')}
    information={t('indexPage.latestSocialEvents.information')}
    eventType="social"
    addScript
  >
    <Fragment slot="bg">
      <div class="absolute inset-0 bg-blue-50 dark:bg-transparent"></div>
    </Fragment>
  </EventsHorizontalSwiper>

  <SocializationPastEventsCta />

  <Stats title={t('dancing.stats.title')} stats={t('dancing.stats.stats')} />

  <FollowSectionSocials section="socialization" ctaText={t('followUs')} />

  <CallToAction
    title={t('dancing.callToAction.title')}
    subtitle={t('dancing.callToAction.subtitle')}
    callToAction={{
      text: t('dancing.callToAction.ctaText'),
      href: translatePath('/contact'),
    }}
  />
</Layout>
