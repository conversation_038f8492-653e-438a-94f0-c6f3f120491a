---
import { Icon } from 'astro-icon/components';
import { I18N } from '~/utils/config';
// import { getBlogPermalink } from '~/utils/permalinks';

import { getLangFromUrl, useTranslations, useTranslatedPath } from '~/i18n/utils';

const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);
const translatePath = useTranslatedPath(lang);

const { textDirection } = I18N;
---

<div class="mx-auto px-6 sm:px-6 max-w-3xl pt-8 md:pt-4 pb-12 md:pb-20">
  <!-- <a class="btn btn-ghost px-3 md:px-3" href={getBlogPermalink()}> -->
  <a class="btn btn-ghost px-3 md:px-3" href={translatePath('posts')}>
    {
      textDirection === 'rtl' ? (
        <Icon name="tabler:chevron-right" class="w-5 h-5 mr-1 -ml-1.5 rtl:-mr-1.5 rtl:ml-1" />
      ) : (
        <Icon name="tabler:chevron-left" class="w-5 h-5 mr-1 -ml-1.5 rtl:-mr-1.5 rtl:ml-1" />
      )
    }
    {t('backToContents')}</a
  >
</div>
