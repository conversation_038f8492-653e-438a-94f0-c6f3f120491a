---
import ListItemAgenda from '../common/ListItemAgenda.astro';

interface Props {
  events: any;
  pathPrefix: string;
}

const { events, pathPrefix } = Astro.props;
---

<ul class="divide-y divide-gray-200 dark:divide-gray-700 space-y-1">
  {events?.map((event) => <ListItemAgenda pathPrefix={pathPrefix} event={event}/>)}
</ul>

<style>
  .list-item:hover {
    @apply bg-gray-200 dark:bg-gray-700; /* Colore compatibile con light/dark mode */
    cursor: pointer;
  }
</style>
