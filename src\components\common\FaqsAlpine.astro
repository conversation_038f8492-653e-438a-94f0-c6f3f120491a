---
import { getLangFromUrl, useTranslations } from '~/i18n/utils';

const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);

// https://devdojo.com/pines/docs/accordion
// https://merakiui.com/components/marketing/faq

export interface Props {
  faqs?: Array<{ answer: string; question: string }>;
}

const { faqs } = Astro.props;

const faqsComputed = faqs?.map((f) => ({
  answer: t(f.answer),
  question: t(f.question),
}));
---

<div id="faqsData" class="hidden">{JSON.stringify(faqsComputed)}</div>

<div
  class="container px-6 py-10 mx-auto"
  x-data="{ 
            activeAccordion: '', 
            setActiveAccordion(id) { 
                this.activeAccordion = (this.activeAccordion == id) ? '' : id 
            } 
        }"
>
  <h1 class="text-2xl font-semibold text-gray-800 lg:text-3xl dark:text-white">FAQ's</h1>

  <hr class="my-6 border-gray-200 dark:border-gray-700" />

  <div>
    <template x-for="(faq, index) in JSON.parse(document.getElementById('faqsData').textContent)" :key="index">
      <div x-data="{ id: $id('accordion' + index) }" class="cursor-pointer group" @click="setActiveAccordion(id)">
        <button class="flex items-center focus:outline-none">
          <svg
            class="flex-shrink-0 w-6 h-6 text-blue-500"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              x-show="activeAccordion==id"
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M20 12H4"></path>
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M12 4v16m8-8H4"
              x-show="activeAccordion!=id"></path>
          </svg>

          <h1 class="mx-4 text-xl text-gray-700 dark:text-white" x-text="faq.question"></h1>
        </button>

        <div x-show="activeAccordion==id" x-collapse x-cloak>
          <div class="flex mt-8 md:mx-10">
            <span class="border border-blue-500"></span>

            <p class="max-w-3xl px-4 text-gray-500 dark:text-gray-300" x-text="faq.answer"></p>
          </div>
        </div>
        <hr class="my-8 border-gray-200 dark:border-gray-700" />
      </div>
    </template>
  </div>
</div>
