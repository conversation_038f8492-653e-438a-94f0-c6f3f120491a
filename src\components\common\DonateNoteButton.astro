---
import Note from '../widgets/Note.astro';
import { getLangFromUrl, useTranslations } from '~/i18n/utils';

const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);

export interface Props {
  link: string;
  targetBlank?: boolean;
}

const { link, targetBlank = false } = Astro.props;
---

<Note>
  <a href={link} target={targetBlank ? '_blank' : ''} class="flex justify-center">
    <button
      type="button"
      class="text-gray-900 bg-[#FBB03C] hover:bg-[#FBB03C]/75 focus:ring-4 focus:outline-none focus:ring-[#FBB03C]/50 font-medium pl-2 pr-4 py-1 text-center inline-flex items-center dark:focus:ring-[#FBB03C]/50 me-2 hover:scale-110 hover:ease-in duration-300"
    >
      <svg
        class="w-8 h-8 me-2 -ms-1 animate-bounce duration-2000"
        aria-hidden="true"
        focusable="false"
        role="img"
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 384 512"
        ><path
          fill="#fff"
          d="M371 140H100c-18 0-20 20 -20 20v184s3 40 40 40h188c41 0 40-40 40 -40v-26h23a89 89 0 000-178m0 132h-23v-86h23a43 43 0 010 86"
        ></path><path
          fill="#f1464d"
          d="M286 219c-10-30-63-33-77 3-15-36-67-33-77-3-7 19-4 35 9 54s68 64 68 64 56-45 68-64 15-35 9-54"></path></svg
      >

      {t('buttonDonate.text')}
    </button>
  </a>
</Note>
