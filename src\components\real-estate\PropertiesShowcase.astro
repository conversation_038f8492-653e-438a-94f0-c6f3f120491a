---
import WidgetWrapper from '~/components/ui/WidgetWrapper.astro';
import Button from '~/components/ui/Button.astro';
import PropertyCard from './PropertyCard.astro';
import type { Widget } from '~/types';

// Types
interface Image {
  url: string;
  alt?: string;
}

interface Apartment {
  id: number;
  price: number;
  address: string;
  images: Image[];
  bedrooms: number;
  bathrooms: number;
  area: number;
  rating: number;
  type: string;
  features: string[];
  mode?: string; // Aggiunto per compatibilità
  Name?: string; // Aggiunto per compatibilità
  Status?: string; // Aggiunto per compatibilità
  Notes?: string; // Aggiunto per compatibilità
  Assignee?: string; // Aggiunto per compatibilità
}

export interface Props extends Widget {
  title?: string;
  linkText?: string;
  linkUrl?: string | URL;
  information?: string;
  properties: Apartment[];
  features?: boolean;
  addScript?: boolean;
  slidesPerView?: {
    mobile?: number;
    tablet?: number;
    desktop?: number;
  };
}

// Default props
const {
  title = await Astro.slots.render('title'),
  linkText,
  linkUrl,
  information = await Astro.slots.render('information'),
  properties,
  features = false,
  addScript = false,
  slidesPerView = {
    mobile: 1,
    tablet: 2,
    desktop: 3,
  },
  id,
  isDark = false,
  classes = {},
  bg = await Astro.slots.render('bg'),
} = Astro.props;

// Constants
const SWIPER_CONFIG = {
  sliderId: 'properties-slider',
  breakpoints: {
    320: {
      slidesPerView: slidesPerView.mobile,
      spaceBetween: 20,
    },
    768: {
      slidesPerView: slidesPerView.tablet,
      spaceBetween: 30,
    },
    1024: {
      slidesPerView: slidesPerView.desktop,
      spaceBetween: 30,
    },
  },
};
---

<WidgetWrapper id={id} isDark={isDark} containerClass={classes?.container} bg={bg}>
  <!-- Header Section -->
  <div class="flex flex-col lg:justify-between lg:flex-row mb-8">
    {
      title && (
        <div class="md:max-w-sm">
          <h2
            class="text-3xl font-bold tracking-tight sm:text-4xl sm:leading-none group font-heading mb-2"
            set:html={title}
          />
          {linkText && linkUrl && (
            <Button variant="link" href={linkUrl}>
              {linkText}
            </Button>
          )}
        </div>
      )
    }
    {information && <p class="text-muted dark:text-slate-400 lg:text-sm lg:max-w-md" set:html={information} />}
  </div>

  <!-- Properties Slider -->
  <div id={SWIPER_CONFIG.sliderId} class="swiper">
    <div class="swiper-wrapper py-6">
      {
        properties.map((property) => (
          <div class="swiper-slide">
            <PropertyCard property={property} showFeatures={true} />
          </div>
        ))
      }
    </div>
    <div class="swiper-button-prev"></div>
    <div class="swiper-button-next"></div>
  </div>
</WidgetWrapper>

<!-- Swiper CSS and JS -->
{
  addScript && (
    <>
      <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css" />
      <script is:inline src="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js" />
    </>
  )
}

<script define:vars={{ config: SWIPER_CONFIG }}>
  const initSwiper = () => {
    return new Swiper(`#${config.sliderId}`, {
      preventClicks: true,
      autoHeight: true,
      slidesPerView: 'auto',
      direction: 'horizontal',
      loop: true,
      navigation: {
        nextEl: '.swiper-button-next',
        prevEl: '.swiper-button-prev',
      },
      breakpoints: config.breakpoints,
    });
  };

  // Initialize Swiper
  document.addEventListener('DOMContentLoaded', () => {
    if (document.querySelector(`#${config.sliderId}`)) {
      const swiper = initSwiper();
    }
  });
</script>
