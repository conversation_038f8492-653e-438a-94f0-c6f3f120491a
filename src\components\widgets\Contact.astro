---
import FormContainer from '~/components/ui/Form.astro';
import Headline from '~/components/ui/Headline.astro';
import WidgetWrapper from '~/components/ui/WidgetWrapper.astro';
import type { Contact } from '~/types';

const {
  title = await Astro.slots.render('title'),
  subtitle = await Astro.slots.render('subtitle'),
  tagline = await Astro.slots.render('tagline'),
  inputs,
  textarea,
  disclaimer,
  button,
  description,

  id,
  isDark = false,
  classes = {},
  bg = await Astro.slots.render('bg'),
} = Astro.props as Contact;
---

<WidgetWrapper id={id} isDark={isDark} containerClass={`max-w-7xl mx-auto ${classes?.container ?? ''}`} bg={bg}>
  <Headline title={title} subtitle={subtitle} tagline={tagline} />

  {
    inputs && (
      <div class="flex flex-col max-w-xl mx-auto rounded-lg backdrop-blur border border-gray-200 dark:border-gray-700 bg-white dark:bg-slate-900 shadow p-4 sm:p-6 lg:p-8 w-full">
        <FormContainer
          inputs={inputs}
          textarea={textarea}
          disclaimer={disclaimer}
          button={button}
          description={description}
        />
      </div>
    )
  }
</WidgetWrapper>
