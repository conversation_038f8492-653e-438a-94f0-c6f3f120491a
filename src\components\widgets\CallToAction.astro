---
import { Icon } from 'astro-icon/components';
import WidgetWrapper from '../ui/WidgetWrapper.astro';
import type { CallToAction, Widget } from '~/types';
import Headline from '../ui/Headline.astro';

interface Props extends Widget {
  title?: string;
  subtitle?: string;
  tagline?: string;
  target?: string;
  callToAction?: CallToAction;
}

const {
  title = await Astro.slots.render('title'),
  subtitle = await Astro.slots.render('subtitle'),
  tagline = await Astro.slots.render('tagline'),
  callToAction = await Astro.slots.render('callToAction'),

  id,
  isDark = false,
  classes = {},
  bg = await Astro.slots.render('bg'),
} = Astro.props as Props;
---

<WidgetWrapper id={id} isDark={isDark} containerClass={`max-w-6xl mx-auto ${classes?.container ?? ''}`} bg={bg}>
  <div
    class="max-w-3xl mx-auto text-center p-6 rounded-md shadow-xl dark:shadow-none dark:border dark:border-slate-600"
  >
    <Headline
      title={title}
      subtitle={subtitle}
      tagline={tagline}
      classes={{
        container: 'mb-0 md:mb-0',
        title: 'text-4xl md:text-4xl font-bold leading-tighter tracking-tighter mb-4 font-heading',
        subtitle: 'text-xl text-muted dark:text-slate-400',
      }}
    />
    {
      typeof callToAction === 'string' ? (
        <Fragment set:html={callToAction} />
      ) : (
        callToAction &&
        callToAction.text &&
        callToAction.href && (
          <div class="mt-6 max-w-xs mx-auto">
            <a class="btn btn-primary w-full sm:w-auto" href={callToAction.href} target={callToAction.targetBlank ? '_blank' : ''} rel="noopener">
              {callToAction.icon && <Icon name={callToAction.icon} class="w-5 h-5 mr-1 -ml-1.5 rtl:-mr-1.5 rtl:ml-1" />}
              {callToAction.text}
            </a>
          </div>
        )
      )
    }
  </div>
</WidgetWrapper>
