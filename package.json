{"name": "livegrancanaria", "version": "1.0.0-beta.1", "description": "A platform that groups events and resources in Gran Canaria.", "private": true, "scripts": {"dev": "astro dev NODE_ENV=development", "start": "astro dev", "build": "astro build NODE_ENV=production", "deploy": "npm run build && netlify deploy --prod --site livegrancanaria.com NODE_ENV=production", "preview": "astro preview", "astro": "astro", "format": "prettier -w .", "lint:eslint": "eslint . --ext .js,.ts,.astro"}, "dependencies": {"@astrojs/rss": "^4.0.12", "@astrojs/sitemap": "^3.4.1", "@astrolib/analytics": "^0.6.1", "@astrolib/seo": "^1.0.0-beta.8", "@fontsource-variable/inter": "^5.2.5", "airtable": "^0.12.2", "astro": "^5.9.2", "astro-compress": "^2.3.6", "astro-icon": "^1.1.5", "dayjs": "^1.11.13", "jimp": "^0.22.12", "limax": "4.1.0", "lodash.merge": "^4.6.2", "marked": "^15.0.7", "mlly": "^1.7.4", "unpic": "^4.1.2"}, "devDependencies": {"@astrojs/mdx": "^4.3.0", "@astrojs/partytown": "^2.1.4", "@astrojs/tailwind": "^6.0.2", "@iconify-json/flat-color-icons": "^1.2.1", "@iconify-json/tabler": "^1.2.17", "@tailwindcss/typography": "^0.5.16", "@types/lodash.merge": "^4.6.9", "@typescript-eslint/eslint-plugin": "^8.24.0", "@typescript-eslint/parser": "^8.24.0", "@vite-pwa/astro": "^0.5.0", "eslint": "^9.20.0", "eslint-plugin-astro": "^1.3.1", "eslint-plugin-jsx-a11y": "^6.10.2", "js-yaml": "^4.1.0", "mdast-util-to-string": "^4.0.0", "netlify-cli": "^18.0.4", "prettier": "^3.5.0", "prettier-plugin-astro": "^0.14.1", "reading-time": "^1.5.0", "tailwind-merge": "^2.3.0", "tailwindcss": "^3.4.3", "typescript": "^5.7.3"}, "engines": {"node": "^18.17.1 || ^20.3.0 || >= 21.0.0"}}