---
import { findImage } from '~/utils/images';
import {
  getImagesOptimized,
  astroAsseetsOptimizer,
  unpicOptimizer,
  type ImageProps,
  type AttributesProps
} from '~/utils/images-optimization';

type Props = ImageProps;
type ImageType = {
  src: string;
  attributes: AttributesProps;
}

const props = Astro.props;

if (props.alt === undefined || props.alt === null) {
  throw new Error();
}

if (typeof props.width === 'string') {
  props.width = parseInt(props.width);
}

if (typeof props.height === 'string') {
  props.height = parseInt(props.height);
}

if (!props.loading) {
  props.loading = 'lazy';
}

if (!props.decoding) {
  props.decoding = 'async';
}

const _image = await findImage(props.src);

let image: ImageType | undefined = undefined;

if (_image !== null && typeof _image === 'object') {
  image = await getImagesOptimized(_image, props, astroAsseetsOptimizer);
} else if (typeof _image === 'string' && (_image.startsWith('http://') || _image.startsWith('https://'))) {
  image = await getImagesOptimized(_image, props, unpicOptimizer);
} else if (_image) {
  image = await getImagesOptimized(_image, props);
}
---

{!image ? <Fragment /> : <img src={image.src} {...image.attributes} />}
