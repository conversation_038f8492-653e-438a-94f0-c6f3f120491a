---
import { APP_BLOG } from '~/utils/config';
import Grid from '~/components/blog/Grid.astro';
import { getBlogPermalink } from '~/utils/permalinks';
import { fetchPosts, filterPostsByLocale, filterPostsByCategory, sortPostsByDate } from '~/utils/blog';
import WidgetWrapper from '~/components/ui/WidgetWrapper.astro';
import type { Widget } from '~/types';

import { getLangFromUrl, useTranslations, useTranslatedPath } from '~/i18n/utils';

const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);
const translatePath = useTranslatedPath(lang);

export interface Props extends Widget {
  title?: string;
  linkText?: string;
  linkUrl?: string | URL;
  information?: string;
  category?: string;
  count?: number;
}

const {
  title = await Astro.slots.render('title'),
  linkText = t('viewAll'),
  linkUrl = getBlogPermalink(),
  information = await Astro.slots.render('information'),
  count = 4,
  category,

  id,
  isDark = false,
  classes = {},
  bg = await Astro.slots.render('bg'),
} = Astro.props;

let posts = APP_BLOG.isEnabled ? await fetchPosts() : [];
posts = sortPostsByDate(posts);
posts = filterPostsByLocale(posts, lang);

if (category) posts = filterPostsByCategory(posts, category.toLowerCase());

const _count = count || 4;
posts = posts ? posts.slice(0, _count) : [];
---

{
  APP_BLOG.isEnabled && !!posts.length ? (
    <WidgetWrapper id={id} isDark={isDark} containerClass={classes?.container} bg={bg}>
      <div class="flex flex-col lg:justify-between lg:flex-row mb-8">
        {title && (
          <div class="md:max-w-sm">
            <h2
              class="text-3xl font-bold tracking-tight sm:text-4xl sm:leading-none group font-heading mb-2"
              set:html={title}
            />
            {APP_BLOG.list.isEnabled && linkText && linkUrl && (
              <a
                class="text-muted dark:text-slate-400 hover:text-primary transition ease-in duration-200 block mb-6 lg:mb-0"
                href={translatePath(linkUrl)}
              >
                {linkText} »
              </a>
            )}
          </div>
        )}

        {information && <p class="text-muted dark:text-slate-400 lg:text-sm lg:max-w-md" set:html={information} />}
      </div>

      <Grid posts={posts} />
    </WidgetWrapper>
  ) : (
    <Fragment />
  )
}
