---
import { getImage } from 'astro:assets';
import serviceBackground from '/src/assets/images/heroes/services.webp';

import { getLangFromUrl, useTranslations } from '~/i18n/utils';

const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);

const optimizedBackground = await getImage({ src: serviceBackground, format: 'webp' });
---

<div
  class="w-full dark:bg-gray-500"
  style={`background-image: url(${optimizedBackground.src}); background-position: center center; background-blend-mode: multiply; background-size: cover;`}
>
  <div class="container flex flex-col flex-wrap content-center justify-center p-4 py-20 mx-auto md:p-10">
    <p class="text-center text-base text-blue-200 font-bold tracking-wide uppercase">{t('services.hero.tagline')}</p>
    <h1 class="text-5xl antialiased font-semibold leadi text-center text-gray-100">{t('services.hero.title')}</h1>
    <p class="pt-2 pb-4 text-xl antialiased text-center text-gray-100">{t('services.hero.subtitle')}</p>
  </div>
</div>
