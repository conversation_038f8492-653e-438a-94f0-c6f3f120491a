---
import Layout from '~/layouts/PageLayout.astro';
import CallToAction from '~/components/widgets/CallToAction.astro';
import Stats from '~/components/widgets/Stats.astro';
import FollowSectionSocials from '~/components/common/FollowSectionSocials.astro';
import DancingHero from '~/components/dancing/DancingHero.astro';
import DancePastEventsCta from '~/components/dancing/DancePastEventsCta.astro';
import { reviews } from '~/connectors/airtable';
import { getLangFromUrl, useTranslations, useTranslatedPath } from '~/i18n/utils';
import Note from '~/components/widgets/Note.astro';
import Testimonials from '~/components/widgets/Testimonials.astro';
import BlogLatestPosts from '~/components/widgets/BlogLatestPosts.astro';
import EventsHorizontalSwiper from '~/components/widgets/EventsHorizontalSwiper.astro';

const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);
const translatePath = useTranslatedPath(lang);

const { Content } = await import(`~/content/standalone/${lang}/socialization.mdx`);

const KEY = 'dancing';
const reviewsFiltered = filterReviewsBySectionName(reviews, KEY);

function filterReviewsBySectionName(reviews, sectionName) {
  return reviews.filter((review) => review?.section_name?.includes(sectionName));
}

const metadata = {
  title: t('dancingPage.metadata.title'),
  description: t('dancingPage.metadata.description'),
};
---

<Layout metadata={metadata}>
  <DancingHero />

  <section class="px-4 py-16 sm:px-6 mx-auto lg:px-8 lg:py-20 max-w-4xl">
    <div class="mx-auto prose prose-lg max-w-none dark:prose-invert">
      <Content />
    </div>
  </section>

  <EventsHorizontalSwiper
    id="latin-dance-events"
    title={t('dancing.latestEvents.title')}
    information={t('dancing.latestEvents.information')}
    eventType="latin"
    addScript
  >
    <Fragment slot="bg">
      <div class="absolute inset-0 bg-blue-50 dark:bg-transparent"></div>
    </Fragment>
  </EventsHorizontalSwiper>

  <BlogLatestPosts id="blog" title={t('latestContents')} category={t('dancing')}>
    <Fragment slot="bg">
      <div class="absolute inset-0 bg-blue-50 dark:bg-transparent"></div>
    </Fragment>
  </BlogLatestPosts>

  <DancePastEventsCta />

  <a href={translatePath('/dancing/schools')} class="uppercase">
    <Note>
      🔎
      {t('lookingForDanceSchool')}
      🕺💃
      <!-- 🩰 -->
      <!-- 🎓 -->
    </Note>
  </a>

  <Stats title={t('dancing.stats.title')} stats={t('dancing.stats.stats')} />

  <Testimonials id="testimonials" title={t('testimonials.title')} testimonials={reviewsFiltered} />

  <FollowSectionSocials section="dancing" ctaText={t('followUs')} />

  <CallToAction
    title={t('dancing.callToAction.title')}
    subtitle={t('dancing.callToAction.subtitle')}
    callToAction={{
      text: t('dancing.callToAction.ctaText'),
      href: translatePath('/contact'),
    }}
  />
</Layout>
