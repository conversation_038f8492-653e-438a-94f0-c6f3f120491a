---
import { getLangFromUrl, useTranslations } from '~/i18n/utils';

// https://merakiui.com/components/marketing/faq 
// https://codepen.io/OsamaDev98/pen/oNOpREp 

const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);

export interface Props {
  faqs?: Array<{ answer: string; question: string }>;
}

const { faqs } = Astro.props;
---

<section id="faq" class="bg-gray-100 dark:bg-gray-800 py-16">
  <div class="container mx-auto px-4">
    <h2 class="text-3xl font-bold text-center mb-8">Frequently Asked Questions</h2>
    <div class="max-w-3xl mx-auto">
      {
        faqs?.map((faq) => (
          <details class="mb-2">
            <summary class="flex justify-between items-center w-full bg-white dark:bg-gray-700 p-3 rounded-lg shadow-md cursor-pointer">
              <span class="font-semibold">{t(faq?.question)}</span>
              <svg
                class="w-4 h-4 text-gray-500 dark:text-gray-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
              </svg>
            </summary>
            <div class="bg-white dark:bg-gray-700 mt-1 p-3 rounded-lg shadow-md">
              <p class="text-gray-600 dark:text-gray-400">{t(faq?.answer)}</p>
            </div>
          </details>
        ))
      }
    </div>
  </div>
</section>
