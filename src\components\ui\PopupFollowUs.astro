---
import { Icon } from 'astro-icon/components';
import { socialLinks } from '~/data/socialLinks';
import { getLangFromUrl, useTranslations } from '~/i18n/utils';

const DEFAULT_SECTION = 'livegc';

const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);

const platformSocialLinks = socialLinks.filter((link) => link.sections.includes(DEFAULT_SECTION));
---

<div class="max-w-7xl mx-auto px-2 py-3 sm:px-4 lg:px-6 bg-white dark:bg-gray-800 text-gray-800 dark:text-white">
  <p class="font-medium text-center">
    <span class="md:hidden">{t('popupFollowUs.mdHidden.text')}</span>
    <span class="hidden md:inline">{t('popupFollowUs.mdInline.text')}</span>
  </p>
  <div class="mt-4 flex flex-wrap justify-center items-center gap-4 sm:gap-6">
    {
      platformSocialLinks.map((social) => (
        <a
          href={social.href}
          class="text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-white transition-colors duration-200 cursor-pointer"
          target="_blank"
          rel="noopener noreferrer"
        >
          <span class="sr-only">{social.name}</span>
          <Icon name={social.icon} class="w-6 h-6 hover:scale-125 hover:ease-in duration-300" />
        </a>
      ))
    }
  </div>
</div>
