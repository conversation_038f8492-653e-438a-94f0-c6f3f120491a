---
import { Icon } from 'astro-icon/components';
import { useTranslations } from '~/i18n/utils';

interface Props {
  title: string;
  price: number;
  address: string;
  lang: string;
}

const { title, price, address, lang } = Astro.props;
const t = useTranslations(lang);
---

<div class="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm">
  <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
    <h1 class="text-3xl font-bold text-gray-900 dark:text-white">{title}</h1>
    <div class="flex items-baseline">
      <span class="text-3xl font-bold text-primary-600 dark:text-primary-400">€{price}</span>
      <span class="ml-1 text-sm text-gray-600 dark:text-gray-400">/ {t('month')}</span>
    </div>
  </div>
  <div class="mt-3 flex items-center text-gray-700 dark:text-gray-300">
    <Icon name="tabler:map-pin" class="w-5 h-5 mr-2 text-primary-500" />
    <span>{address}</span>
  </div>
</div>
