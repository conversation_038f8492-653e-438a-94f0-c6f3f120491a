---
import { twMerge } from 'tailwind-merge';
import type { ItemGrid } from '~/types';
import CTA from './CTA.astro';
import { Icon } from 'astro-icon/components';

const { items = [], columns, defaultIcon = '', classes = {} } = Astro.props as ItemGrid;

const {
  container: containerClass = '',
  panel: panelClass = '',
  title: titleClass = '',
  description: descriptionClass = '',
  icon: defaultIconClass = 'text-primary',
  action: actionClass = '',
} = classes;
---

{
  items && (
    <div
      class={twMerge(
        `grid mx-auto gap-8 md:gap-y-12 ${
          columns === 4
            ? 'lg:grid-cols-4 md:grid-cols-3 sm:grid-cols-2'
            : columns === 3
            ? 'lg:grid-cols-3 sm:grid-cols-2'
            : columns === 2
            ? 'sm:grid-cols-2 '
            : ''
        }`,
        containerClass
      )}
    >
      {items.map(({ title, description, icon, callToAction, classes: itemClasses = {} }) => (
        <div>
          <div class={twMerge('flex flex-row max-w-md', panelClass, itemClasses?.panel)}>
            <div class="flex justify-center">
              {(icon || defaultIcon) && (
                <Icon
                  name={icon || defaultIcon}
                  class={twMerge('w-7 h-7 mr-2 rtl:mr-0 rtl:ml-2', defaultIconClass, itemClasses?.icon)}
                />
              )}
            </div>
            <div class="mt-0.5">
              {title && <h3 class={twMerge('text-xl font-bold', titleClass, itemClasses?.title)}>{title}</h3>}
              {description && (
                <p
                  class={twMerge(`${title ? 'mt-3' : ''} text-muted`, descriptionClass, itemClasses?.description)}
                  set:html={description}
                />
              )}
              {callToAction && (
                <div
                  class={twMerge(
                    `${title || description ? 'mt-3' : ''} text-primary cursor-pointer`,
                    actionClass,
                    itemClasses?.actionClass
                  )}
                >
                  <CTA callToAction={callToAction} />
                </div>
              )}
            </div>
          </div>
        </div>
      ))}
    </div>
  )
}
