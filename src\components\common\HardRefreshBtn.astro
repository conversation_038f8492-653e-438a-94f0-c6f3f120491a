---
import { Icon } from 'astro-icon/components';

interface Props {
  text: string;
}

const { text } = Astro.props;
---

<div
  id="refresh-banner"
  tabindex="-1"
  class="cursor-pointer fixed bottom-0 left-0 z-50 flex justify-between w-full p-4 border-t border-gray-200 bg-gray-50 dark:bg-gray-700 dark:border-gray-600"
>
  <div class="flex items-center mx-auto">
    <p class="flex items-center text-sm font-normal text-gray-500 dark:text-gray-400">
      <span class="inline-flex p-1 mr-3 bg-gray-200 rounded-full dark:bg-gray-600 w-6 h-6 items-center justify-center">
        <Icon name="tabler:refresh" class="w-3.5 h-3.5 inline-block -mt-0.5 dark:text-gray-400" />
        <span class="sr-only">Refresh</span>
      </span>
      <span>{text}</span>
    </p>
  </div>
  <div class="flex items-center">
    <button
      data-dismiss-target="#bottom-banner"
      type="button"
      class="flex-shrink-0 inline-flex justify-center w-7 h-7 items-center text-gray-400 hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm p-1.5 dark:hover:bg-gray-600 dark:hover:text-white"
    >
      <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
        <path
          stroke="currentColor"
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"></path>
      </svg>
      <span class="sr-only">Close banner</span>
    </button>
  </div>
</div>

<script>
  const button = document.getElementById('refresh-banner');
  if (button) button.addEventListener('click', () => location.reload(true));
</script>
