---
import Layout from '~/layouts/LandingLayout.astro';

import Hero from '~/components/widgets/Hero.astro';
import CallToAction from '~/components/widgets/CallToAction.astro';

const metadata = {
  title: 'Product Details Landing Page Demo',
};
---

<Layout metadata={metadata}>
  <!-- Hero2 Widget ******************* -->

  <Hero
    tagline="Product Details Demo"
    title="Product Landing Page: Showcase with Precision and Passion!"
    subtitle="Step-by-step guide to designing a Landing Page that highlights every facet of your product or service."
    callToAction={{ text: 'Call to Action', href: '#', icon: 'tabler:square-rounded-arrow-right' }}
    callToAction2={{ text: 'Learn more', href: '#' }}
    image={{
      src: 'https://images.unsplash.com/photo-1473188588951-666fce8e7c68?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2174&q=80',
      alt: 'A spotlight on a product. Product Details Landing Page Demo',
    }}
  />

  <CallToAction
    title="Coming soon"
    subtitle="We are working on the content of these demo pages. You will see them very soon. Stay tuned Stay tuned!"
    callToAction={{
      text: 'Get template',
      href: 'https://github.com/onwidget/astrowind',
      icon: 'tabler:download',
    }}
  />
</Layout>
