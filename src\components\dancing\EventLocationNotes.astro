---
import { Icon } from 'astro-icon/components';

interface Props {
  locationNotes: string;
}

const { locationNotes } = Astro.props;
---

<div
  class="flex w-full overflow-hidden mt-8 border border-gray-200 dark:border-gray-700 bg-white dark:bg-slate-900 shadow"
>
  <div class="flex items-center justify-center w-12 bg-primary text-white">
    <Icon name="tabler:info-circle" width={60} height={60} class="inline-block m-2" />
  </div>

  {
    locationNotes && (
      <div class="px-4 -mx-3">
        <div class="mx-3">
          <p
            class="text-sm text-gray-600 dark:text-gray-200"
            style="white-space: pre-line;"
            set:html={locationNotes}
          />
        </div>
      </div>
    )
  }
</div>
