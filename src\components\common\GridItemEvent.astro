---
import type { EventInfo } from '~/types';
import card_placeholder from '~/assets/images/common/400x400.svg';
import slugify from 'limax';
import { Image } from 'astro:assets';
import { getLangFromUrl, useTranslations, useTranslatedPath } from '~/i18n/utils';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import es from 'dayjs/locale/es';

// Setup dayjs plugins
dayjs.extend(utc);
dayjs.extend(timezone);

// Constants
const TZ = 'Europe/Madrid';
const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);
const translatePath = useTranslatedPath(lang);

interface Props {
  event: EventInfo;
  flyersPreview?: boolean;
  pathPrefix?: string;
}

const { event, flyersPreview = false, pathPrefix = '' } = Astro.props;

// Compute event properties
const hasFlyerEn = !!event?.flyer;
const hasFlyerEs = !!event?.flyer_es;
const hasOfficialEvent = hasFlyerEn || hasFlyerEs;
const eventName = hasOfficialEvent ? event?.name : event.slug;
const eventDate =
  dayjs(event?.start)
    .locale(lang === 'es' ? es : lang)
    .tz(TZ)
    .format('DD MMM YYYY') || '';

const flyer = hasOfficialEvent
  ? lang === 'es' && hasFlyerEs
    ? event?.flyer_es[0].url
    : event?.flyer[0].url
  : card_placeholder;

const path = `${pathPrefix}/${slugify(t(event.slug))}`;

const getEventsCount = (count?: number) => (count ? `${count} ${t(count === 1 ? 'event' : 'events')}` : t('noEvents'));
---

<a href={translatePath(path)} aria-label={eventName} class="block w-full">
  <div
    class:list={[
      'max-w-xs mx-auto overflow-hidden rounded-lg shadow-lg',
      'transform transition-transform duration-300 hover:scale-105',
      `day-${event?.day_of_the_week}`,
    ]}
  >
    {
      hasOfficialEvent ? (
        <Image
          src={flyer}
          alt={t(event?.excerpt_t) || ''}
          loading="eager"
          width={320}
          height={320}
          decoding="async"
          class="w-full aspect-square object-cover"
        />
      ) : (
        <div class="relative">
          <Image
            src={flyer}
            alt={t(event?.excerpt_t) || ''}
            loading="eager"
            width={320}
            height={320}
            decoding="async"
            class="w-full aspect-square object-cover"
          />
          <div class="absolute inset-0 bg-black/70">
            {flyersPreview ? (
              <div class="grid grid-cols-2 md:grid-cols-3 gap-2 p-3">
                {event?.other_events?.map((f, i) => (
                  <img key={i} class="rounded" src={f.url} alt={`flyer-${i}`} loading="lazy" />
                ))}
              </div>
            ) : (
              <div class="flex items-center justify-center h-full">
                <h5 class="text-base md:text-lg font-bold text-white">{getEventsCount(event?.other_events?.length)}</h5>
              </div>
            )}
          </div>
        </div>
      )
    }
    <div class="flex items-center justify-between px-4 py-3 bg-gray-100 dark:bg-gray-800">
      <h2
        class:list={[
          'text-base font-semibold capitalize truncate',
          hasOfficialEvent ? 'text-orange-500 dark:text-orange-400 font-bold' : 'dark:text-white',
        ]}
      >
        {t(eventName)}
      </h2>
      <small class="text-yellow-600 dark:text-yellow-400 font-medium ml-2 whitespace-nowrap">
        {eventDate}
      </small>
    </div>
  </div>
</a>

<script>
  const today = new Date();
  const dayOfWeek = today.getDay() || 7;
  document.querySelectorAll(`.day-${dayOfWeek}`).forEach((el) => el.classList.add('border-selected'));
</script>

<style>
  .border-selected {
    @apply p-1;
    background: conic-gradient(from 180deg at 50% 50%, #f09433, #e6683c, #dc2743, #cc2366, #bc1888, #e6683c, #f09433);
  }
</style>
