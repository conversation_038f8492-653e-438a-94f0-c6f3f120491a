---
// https://uiverse.io/cards?orderBy=favorites&theme=light
// https://www.penguinui.com/components/card
import MarketplaceHighlight from '~/components/ui/MarketplaceHighlight.astro';
import WeeklyEvents from '~/components/widgets/WeeklyEvents.astro';
import Layout from '~/layouts/PageLayout.astro';
---

<Layout metadata={}>
  <main class="container mx-auto px-4">
    <!-- Latest Blog Posts -->
    <section class="my-12">
      <h2 class="text-3xl font-bold mb-6">Latest Blog Posts</h2>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {
          [1, 2, 3].map((post) => (
            <div class="bg-gray-100 dark:bg-gray-800 p-6 rounded-lg">
              <h3 class="text-xl font-semibold mb-2">Blog Post {post}</h3>
              <p class="text-gray-600 dark:text-gray-400">Lorem ipsum dolor sit amet, consectetur adipiscing elit.</p>
            </div>
          ))
        }
      </div>
    </section>

    <!-- Services -->
    <section class="my-12">
      <h2 class="text-3xl font-bold mb-6">Our Services</h2>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {
          [1, 2, 3].map((service) => (
            <div class="bg-gray-100 dark:bg-gray-800 p-6 rounded-lg">
              <div class="w-full h-40 bg-gray-300 dark:bg-gray-700 rounded-lg mb-4" />
              <h3 class="text-xl font-semibold mb-2">Service {service}</h3>
              <p class="text-gray-600 dark:text-gray-400">Description of service {service}.</p>
            </div>
          ))
        }
      </div>
    </section>

    <!-- Agenda -->
    <section class="my-12">
      <h2 class="text-3xl font-bold mb-6">Upcoming Events</h2>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        {
          ['Dancing', 'Social'].map((section, index) => (
            <div class={`p-6 ${index === 0 ? 'bg-gray-200 dark:bg-gray-900' : 'bg-gray-300 dark:bg-gray-800'}`}>
              <h3 class="text-2xl font-semibold mb-4">{section} Agenda</h3>
              <WeeklyEvents />
            </div>
          ))
        }
      </div>
    </section>

    <!-- Housing / Real Estate -->
    <section class="my-12">
      <h2 class="text-3xl font-bold mb-6">Featured Properties</h2>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {
          [1, 2, 3].map((property) => (
            <div class="bg-gray-100 dark:bg-gray-800 p-6 rounded-lg">
              <div class="w-full h-48 bg-gray-300 dark:bg-gray-700 rounded-lg mb-4" />
              <h3 class="text-xl font-semibold mb-2">Property {property}</h3>
              <p class="text-gray-600 dark:text-gray-400">Description of property {property}.</p>
            </div>
          ))
        }
      </div>
    </section>

    <MarketplaceHighlight/>

    <!-- Newsletter Sign-up -->
    <section class="my-12 bg-gray-100 dark:bg-gray-800 p-8 rounded-lg">
      <h2 class="text-3xl font-bold mb-4">Subscribe to Our Newsletter</h2>
      <p class="mb-4">Stay updated with our latest news and offers.</p>
      <form class="flex flex-col sm:flex-row gap-4">
        <input type="email" placeholder="Enter your email" class="flex-grow px-4 py-2 rounded-lg" />
        <button type="submit" class="bg-blue-500 text-white px-6 py-2 rounded-lg hover:bg-blue-600">Subscribe</button>
      </form>
    </section>

    <!-- Footer -->
    <footer class="mt-12 py-6 border-t border-gray-200 dark:border-gray-700">
      <div class="flex justify-between items-center">
        <p>&copy; 2023 Your Company. All rights reserved.</p>
        <div class="flex space-x-4">
          <a href="#" class="hover:text-blue-500">Privacy Policy</a>
          <a href="#" class="hover:text-blue-500">Terms of Service</a>
        </div>
      </div>
    </footer>
  </main>
</Layout>
