export const uploadImageAndGetUrl = async (imageBuffer) => {
    const base = 'https://filetransfer.pulpo.cloud';
    const imageBase64 = imageBuffer?.toString('base64');
    const mimetype = 'image/png';
    const file = imageBase64;

    const response = await fetch(`${base}/api/upload`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ file, mimetype })
    });

    if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
    else {
        const data = await response.json();
        return `${base}${data.url}`;
    }
}