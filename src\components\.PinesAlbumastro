---
import Headline from '~/components/ui/Headline.astro';

export interface Props {
  title?: string;
  subtitle?: string;
  imageGallery?: Array<{ src: string; alt: string }>;
}

const { title, subtitle, imageGallery } = Astro.props;
---

{
  title && (
    <div class="my-8">
      <Headline title={title} tagline={subtitle} />
    </div>
  )
}

<div id="imageGalleryData" class="hidden">{JSON.stringify(imageGallery || [])}</div>

<div
  x-data="imageGallery"
  @keydown.window.right="imageGalleryNext()"
  @keydown.window.left="imageGalleryPrev()"
  class="w-full h-full select-none"
>
  <div
    class="max-w-6xl mx-auto duration-1000 delay-300 opacity-0 select-none ease animate-fade-in-view"
    style="translate: none; rotate: none; scale: none; opacity: 1; transform: translate(0px, 0px);"
  >
    <ul x-ref="gallery" id="gallery" class="grid grid-cols-2 gap-5 lg:grid-cols-5">
      <template x-for="(image, index) in images" :key="index">
        <li>
          <img
            @click="imageGalleryOpen"
            :src="image.src"
            :alt="image.alt"
            :data-index="index"
            class="object-cover select-none w-full h-auto bg-gray-200 rounded cursor-zoom-in aspect-[5/6] lg:aspect-[2/3] xl:aspect-[3/4] hover:opacity-90 transition-opacity duration-300"
          />
        </li>
      </template>
    </ul>
  </div>

  <template x-if="imageGalleryOpened">
    <div
      x-show="imageGalleryOpened"
      x-transition:enter="transition ease-in-out duration-300"
      x-transition:enter-start="opacity-0"
      x-transition:leave="transition ease-in-out duration-300"
      x-transition:leave-end="opacity-0"
      @click="imageGalleryClose"
      @keydown.window.escape="imageGalleryClose"
      class="fixed inset-0 z-[99] flex items-center justify-center bg-black/80 backdrop-blur-sm select-none cursor-zoom-out"
    >
      <div class="relative flex items-center justify-center w-11/12 xl:w-4/5 h-11/12">
        <!-- Loading Spinner -->
        <div x-show="loading" class="absolute inset-0 flex items-center justify-center z-10">
          <div class="w-12 h-12 border-4 border-white border-t-transparent rounded-full animate-spin"></div>
        </div>

        <!-- Navigation Buttons -->
        <button
          @click.stop="imageGalleryPrev"
          class="absolute left-0 flex items-center justify-center text-white translate-x-10 rounded-full cursor-pointer xl:-translate-x-24 2xl:-translate-x-32 bg-white/10 w-14 h-14 hover:bg-white/20 transition-colors duration-300"
          :class="{ 'opacity-50 cursor-not-allowed': loading }"
          :disabled="loading"
        >
          <svg
            class="w-6 h-6"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.5"
            stroke="currentColor"
          >
            <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 19.5L8.25 12l7.5-7.5"></path>
          </svg>
        </button>

        <!-- Main Image -->
        <img
          x-show="imageGalleryActiveUrl"
          x-transition:enter="transition ease-out duration-300"
          x-transition:enter-start="opacity-0 scale-95"
          x-transition:enter-end="opacity-100 scale-100"
          x-transition:leave="transition ease-in duration-300"
          x-transition:leave-start="opacity-100 scale-100"
          x-transition:leave-end="opacity-0 scale-95"
          class="object-contain object-center w-full h-full select-none cursor-zoom-out max-h-[90vh]"
          :src="imageGalleryActiveUrl"
          :alt="images[imageGalleryImageIndex]?.alt"
          :class="{ 'opacity-50': loading }"
        />

        <button
          @click.stop="imageGalleryNext"
          class="absolute right-0 flex items-center justify-center text-white -translate-x-10 rounded-full cursor-pointer xl:translate-x-24 2xl:translate-x-32 bg-white/10 w-14 h-14 hover:bg-white/20 transition-colors duration-300"
          :class="{ 'opacity-50 cursor-not-allowed': loading }"
          :disabled="loading"
        >
          <svg
            class="w-6 h-6"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.5"
            stroke="currentColor"
          >
            <path stroke-linecap="round" stroke-linejoin="round" d="M8.25 4.5l7.5 7.5-7.5 7.5"></path>
          </svg>
        </button>

        <!-- Image Counter -->
        <div
          class="absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-black/50 text-white px-4 py-2 rounded-full text-sm"
        >
          <span x-text="(imageGalleryImageIndex + 1) + ' / ' + images.length"></span>
        </div>
      </div>
    </div>
  </template>
</div>

<script>
  document.addEventListener('alpine:init', () => {
    Alpine.data('imageGallery', () => ({
      imageGalleryOpened: false,
      imageGalleryActiveUrl: null,
      imageGalleryImageIndex: null,
      loading: false,
      images: [],

      init() {
        this.images = JSON.parse(document.getElementById('imageGalleryData')?.textContent || '[]');
      },

      preloadImage(src) {
        return new Promise((resolve, reject) => {
          const img = new Image();
          img.onload = () => resolve(src);
          img.onerror = reject;
          img.src = src;
        });
      },

      async changeImage(newIndex) {
        if (!this.images.length) return;

        this.loading = true;
        const newUrl = this.images[newIndex].src;
        try {
          await this.preloadImage(newUrl);
          this.imageGalleryImageIndex = newIndex;
          this.imageGalleryActiveUrl = newUrl;
        } catch (error) {
          console.error("Errore nel caricamento dell'immagine:", error);
        }
        this.loading = false;
      },

      async imageGalleryOpen(event) {
        if (!event.target.dataset.index) return;
        const index = parseInt(event.target.dataset.index);
        await this.changeImage(index);
        this.imageGalleryOpened = true;
      },

      imageGalleryClose() {
        this.imageGalleryOpened = false;
        setTimeout(() => {
          this.imageGalleryActiveUrl = null;
          this.imageGalleryImageIndex = null;
        }, 300);
      },

      async imageGalleryNext() {
        if (this.loading) return;
        const newIndex =
          this.imageGalleryImageIndex === this.images.length - 1 ? 0 : parseInt(this.imageGalleryImageIndex) + 1;
        await this.changeImage(newIndex);
      },

      async imageGalleryPrev() {
        if (this.loading) return;
        const newIndex =
          this.imageGalleryImageIndex === 0 ? this.images.length - 1 : parseInt(this.imageGalleryImageIndex) - 1;
        await this.changeImage(newIndex);
      },
    }));
  });
</script>
