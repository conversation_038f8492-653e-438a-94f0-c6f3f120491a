---
import { Icon } from 'astro-icon/components';

export interface Props {
  class?: string;
}

const { class: className = 'inline-block' } = Astro.props;

// const socialLinks = [
//   {
//     ariaLabel: 'Join us on Facebook',
//     icon: 'tabler:brand-facebook',
//     href: 'https://www.facebook.com/groups/grancanariadancinggroup',
//     color: 'blue',
//   },
//   {
//     ariaLabel: 'Follow us on Instagram',
//     icon: 'tabler:brand-instagram',
//     href: 'https://www.instagram.com/grancanariadancinggroup/',
//     color: 'red',
//   },
//   {
//     ariaLabel: 'Whatsapp',
//     text: 'Join us on Whatsapp',
//     icon: 'tabler:brand-whatsapp',
//     href: 'https://www.instagram.com/grancanariadancinggroup/',
//     color: 'green',
//   },
// ];
---

<div class={className}>
  <span class="align-super font-bold text-gray-400 dark:text-slate-400">Socials:</span>
  <div class="flex">
    <div class="rounded-md bg-white/5 p-2 ring-1 ring-white/10">
      <Icon
        name="tabler:brand-instagram"
        class="w-6 h-6 text-gray-400 dark:text-slate-500 hover:text-black dark:hover:text-slate-300"
      />
    </div>
    <div class="rounded-md bg-white/5 p-2 ring-1 ring-white/10">
      <Icon
        name="tabler:brand-facebook"
        class="w-6 h-6 text-gray-400 dark:text-slate-500 hover:text-black dark:hover:text-slate-300"
      />
    </div>
    <div class="rounded-md bg-white/5 p-2 ring-1 ring-white/10">
      <Icon
        name="tabler:brand-whatsapp"
        class="w-6 h-6 text-gray-400 dark:text-slate-500 hover:text-black dark:hover:text-slate-300"
      />
    </div>
  </div>
</div>
