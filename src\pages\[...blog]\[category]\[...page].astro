---
import type { InferGetStaticPropsType, GetStaticPaths } from 'astro';
import { blogCategoryRobots, getStaticPathsBlogCategory, filterPostsByLocale } from '~/utils/blog';

import Layout from '~/layouts/PageLayout.astro';
import BlogList from '~/components/blog/List.astro';
import Headline from '~/components/blog/Headline.astro';
import Pagination from '~/components/blog/Pagination.astro';

import { getLangFromUrl } from '~/i18n/utils';

const lang = getLangFromUrl(Astro.url);

export const prerender = true;

export const getStaticPaths = (async ({ paginate }) => {
  return await getStaticPathsBlogCategory({ paginate });
}) satisfies GetStaticPaths;

type Props = InferGetStaticPropsType<typeof getStaticPaths> & { category: string };

const { page, category } = Astro.props as Props;

const currentPage = page.currentPage ?? 1;

const posts = filterPostsByLocale(page.data, lang);

const metadata = {
  title: `Category '${category}' ${currentPage > 1 ? ` — Page ${currentPage}` : ''}`,
  robots: {
    index: blogCategoryRobots?.index,
    follow: blogCategoryRobots?.follow,
  },
};
---

<Layout metadata={metadata}>
  <section class="px-4 md:px-6 py-12 sm:py-16 lg:py-20 mx-auto max-w-4xl">
    <Headline><span class="capitalize">{category.replaceAll('-', ' ')}</span></Headline>
    <!-- <BlogList posts={page.data} /> -->
    <BlogList posts={posts} />
    <Pagination prevUrl={page.url.prev} nextUrl={page.url.next} />
  </section>
</Layout>
