<main class="relative min-h-screen flex flex-col justify-center bg-slate-50 overflow-hidden">
  <div class="w-full max-w-5xl mx-auto px-4 md:px-6 py-24">
    <!-- Testimonials with Tooltip -->
    <section class="text-center">
      <div class="font-nycd text-2xl text-indigo-500 subpixel-antialiased mb-4">
        <span class="relative inline-flex">
          <span>Our promise</span>
          <svg class="fill-indigo-500 absolute bottom-0" xmlns="http://www.w3.org/2000/svg" width="132" height="4">
            <path
              fill-opacity=".4"
              fill-rule="evenodd"
              d="M131.014 2.344s-39.52 1.318-64.973 1.593c-25.456.24-65.013-.282-65.013-.282C-.34 3.623-.332 1.732.987 1.656c0 0 39.52-1.32 64.973-1.593 25.455-.24 65.012.282 65.012.282 1.356.184 1.37 1.86.042 1.999"
            ></path>
          </svg>
        </span>
      </div>
      <div class="text-5xl leading-tight font-bold text-slate-900">
        <span class="[&:has(~.active)]:opacity-25 [&.active~*]:opacity-25 transition-opacity duration-200"
          >We'll help you boost your revenues</span
        >
        <div
          class="[&:has(~.active)]:opacity-25 [&.active~*]:opacity-25 transition-opacity duration-200 relative inline-flex justify-center w-[52px] h-[52px] align-middle -translate-y-1 z-50"
          x-data="{ open: false }"
          :class="{ 'active': open }"
          @mouseover.outside="open = false"
          @focusout="await $nextTick();!$el.contains($focus.focused()) && (open = false)"
        >
          <button
            class="h-full w-full focus-visible:outline-none focus-visible:ring focus-visible:ring-indigo-300 rounded-[20px] rotate-[4deg] transition duration-200 ease-[cubic-bezier(.5,.85,.25,1.8)] delay-100"
            :class="{ 'rotate-0': open }"
            aria-labelledby="testimonial-01"
            @mouseover="open = true"
            @focus="open = true"
          >
            <img
              class="absolute top-1/2 -translate-y-1/2 rounded-[inherit]"
              src="./testimonial-01.jpg"
              width="52"
              height="52"
              alt="Testimonial 01"
            />
          </button>
          <div
            id="testimonial-01"
            role="tooltip"
            class="absolute top-full pt-5 [&[x-cloak]]:hidden"
            x-ref="tooltip"
            x-cloak
          >
            <div
              class="relative w-80 after:absolute after:-top-1.5 after:left-1/2 after:-translate-x-1/2 after:h-3 after:w-3 after:rounded-tl-sm after:rotate-45 after:bg-slate-900"
              x-show="open"
              x-transition:enter="transition ease-[cubic-bezier(.5,.85,.25,1.8)] duration-200 delay-100"
              x-transition:enter-start="opacity-0 translate-y-2"
              x-transition:enter-end="opacity-100 translate-y-0"
              x-transition:leave="transition ease-[cubic-bezier(.5,.85,.25,1.8)] duration-100 delay-100"
              x-transition:leave-start="opacity-100"
              x-transition:leave-end="opacity-0"
            >
              <div
                class="relative bg-slate-900 p-5 rounded-3xl shadow-xl text-left text-sm text-slate-200 font-medium space-y-3"
                x-init="$watch('open', value => { $nextTick(() => {
                                        $refs.tooltip.getBoundingClientRect().left < 0 ? $el.style.left = Math.abs($refs.tooltip.getBoundingClientRect().left) + $root.getBoundingClientRect().left - 4 + 'px' : $el.style.left = null;
                                        $refs.tooltip.getBoundingClientRect().right > document.documentElement.offsetWidth ? $el.style.right = Math.abs($refs.tooltip.getBoundingClientRect().right) - $root.getBoundingClientRect().right - 4 + 'px' : $el.style.right = null;
                                    } )} )"
              >
                <svg
                  class="fill-indigo-500"
                  xmlns="http://www.w3.org/2000/svg"
                  width="17"
                  height="14"
                  aria-hidden="true"
                >
                  <path
                    fill-rule="nonzero"
                    d="M2.014 3.68c.276-1.267.82-2.198 1.629-2.79C4.453.295 5.627 0 7.167 0c.514 0 .908.02 1.185.061L5.035 10.49c-.75 2.494-2.429 3.66-5.035 3.496L2.014 3.68Zm8.648 0c.237-1.227.77-2.147 1.6-2.76C13.09.307 14.274 0 15.814 0c.514 0 .909.02 1.185.061L13.683 10.49c-.79 2.494-2.468 3.66-5.035 3.496L10.662 3.68Z"
                  ></path>
                </svg>
                <p>
                  This component is AWESOME. The hover feature is well-thought-out. Even the smaller details, like using
                  colors, really helps everything stay organized. Cruip is amazing and I really enjoy using it.
                </p>
                <p>
                  Mary Smith <span class="text-slate-600">-</span>
                  <span class="text-slate-400">Software Engineer</span>
                </p>
              </div>
            </div>
          </div>
        </div>
        <span class="[&:has(~.active)]:opacity-25 [&.active~*]:opacity-25 transition-opacity duration-200"
          >manage payrolls</span
        >
        <div
          class="[&:has(~.active)]:opacity-25 [&.active~*]:opacity-25 transition-opacity duration-200 relative inline-flex justify-center w-[52px] h-[52px] align-middle -translate-y-1 z-40"
          x-data="{ open: false }"
          :class="{ 'active': open }"
          @mouseover.outside="open = false"
          @focusout="await $nextTick();!$el.contains($focus.focused()) && (open = false)"
        >
          <button
            class="h-full w-full focus-visible:outline-none focus-visible:ring focus-visible:ring-indigo-300 rounded-[20px] -rotate-[4deg] transition duration-200 ease-[cubic-bezier(.5,.85,.25,1.8)] delay-100"
            :class="{ 'rotate-0': open }"
            aria-labelledby="testimonial-02"
            @mouseover="open = true"
            @focus="open = true"
          >
            <img
              class="absolute top-1/2 -translate-y-1/2 rounded-[inherit]"
              src="./testimonial-02.jpg"
              width="52"
              height="52"
              alt="Testimonial 02"
            />
          </button>
          <div
            id="testimonial-02"
            role="tooltip"
            class="absolute top-full pt-5 [&[x-cloak]]:hidden"
            x-ref="tooltip"
            x-cloak
          >
            <div
              class="relative w-80 after:absolute after:-top-1.5 after:left-1/2 after:-translate-x-1/2 after:h-3 after:w-3 after:rounded-tl-sm after:rotate-45 after:bg-slate-900"
              x-show="open"
              x-transition:enter="transition ease-[cubic-bezier(.5,.85,.25,1.8)] duration-200 delay-100"
              x-transition:enter-start="opacity-0 translate-y-2"
              x-transition:enter-end="opacity-100 translate-y-0"
              x-transition:leave="transition ease-[cubic-bezier(.5,.85,.25,1.8)] duration-100 delay-100"
              x-transition:leave-start="opacity-100"
              x-transition:leave-end="opacity-0"
            >
              <div
                class="relative bg-slate-900 p-5 rounded-3xl shadow-xl text-left text-sm text-slate-200 font-medium space-y-3"
                x-init="$watch('open', value => { $nextTick(() => {
                                        $refs.tooltip.getBoundingClientRect().left < 0 ? $el.style.left = Math.abs($refs.tooltip.getBoundingClientRect().left) + $root.getBoundingClientRect().left - 4 + 'px' : $el.style.left = null;
                                        $refs.tooltip.getBoundingClientRect().right > document.documentElement.offsetWidth ? $el.style.right = Math.abs($refs.tooltip.getBoundingClientRect().right) - $root.getBoundingClientRect().right - 4 + 'px' : $el.style.right = null;
                                    } )} )"
              >
                <svg
                  class="fill-indigo-500"
                  xmlns="http://www.w3.org/2000/svg"
                  width="17"
                  height="14"
                  aria-hidden="true"
                >
                  <path
                    fill-rule="nonzero"
                    d="M2.014 3.68c.276-1.267.82-2.198 1.629-2.79C4.453.295 5.627 0 7.167 0c.514 0 .908.02 1.185.061L5.035 10.49c-.75 2.494-2.429 3.66-5.035 3.496L2.014 3.68Zm8.648 0c.237-1.227.77-2.147 1.6-2.76C13.09.307 14.274 0 15.814 0c.514 0 .909.02 1.185.061L13.683 10.49c-.79 2.494-2.468 3.66-5.035 3.496L10.662 3.68Z"
                  ></path>
                </svg>
                <p>
                  This component is AWESOME. The hover feature is well-thought-out. Even the smaller details, like using
                  colors, really helps everything stay organized. Cruip is amazing and I really enjoy using it.
                </p>
                <p>
                  Mary Smith <span class="text-slate-600">-</span>
                  <span class="text-slate-400">Software Engineer</span>
                </p>
              </div>
            </div>
          </div>
        </div>
        <span class="[&:has(~.active)]:opacity-25 [&.active~*]:opacity-25 transition-opacity duration-200"
          >and save up to 50+ hours in duties every month</span
        >
        <div
          class="[&:has(~.active)]:opacity-25 [&.active~*]:opacity-25 transition-opacity duration-200 relative inline-flex justify-center w-[52px] h-[52px] align-middle -translate-y-1 z-30"
          x-data="{ open: false }"
          :class="{ 'active': open }"
          @mouseover.outside="open = false"
          @focusout="await $nextTick();!$el.contains($focus.focused()) && (open = false)"
        >
          <button
            class="h-full w-full focus-visible:outline-none focus-visible:ring focus-visible:ring-indigo-300 rounded-[20px] rotate-[4deg] transition duration-200 ease-[cubic-bezier(.5,.85,.25,1.8)] delay-100"
            :class="{ 'rotate-0': open }"
            aria-labelledby="testimonial-03"
            @mouseover="open = true"
            @focus="open = true"
          >
            <img
              class="absolute top-1/2 -translate-y-1/2 rounded-[inherit]"
              src="./testimonial-03.jpg"
              width="52"
              height="52"
              alt="Testimonial 03"
            />
          </button>
          <div
            id="testimonial-03"
            role="tooltip"
            class="absolute top-full pt-5 [&[x-cloak]]:hidden"
            x-ref="tooltip"
            x-cloak
          >
            <div
              class="relative w-80 after:absolute after:-top-1.5 after:left-1/2 after:-translate-x-1/2 after:h-3 after:w-3 after:rounded-tl-sm after:rotate-45 after:bg-slate-900"
              x-show="open"
              x-transition:enter="transition ease-[cubic-bezier(.5,.85,.25,1.8)] duration-200 delay-100"
              x-transition:enter-start="opacity-0 translate-y-2"
              x-transition:enter-end="opacity-100 translate-y-0"
              x-transition:leave="transition ease-[cubic-bezier(.5,.85,.25,1.8)] duration-100 delay-100"
              x-transition:leave-start="opacity-100"
              x-transition:leave-end="opacity-0"
            >
              <div
                class="relative bg-slate-900 p-5 rounded-3xl shadow-xl text-left text-sm text-slate-200 font-medium space-y-3"
                x-init="$watch('open', value => { $nextTick(() => {
                                        $refs.tooltip.getBoundingClientRect().left < 0 ? $el.style.left = Math.abs($refs.tooltip.getBoundingClientRect().left) + $root.getBoundingClientRect().left - 4 + 'px' : $el.style.left = null;
                                        $refs.tooltip.getBoundingClientRect().right > document.documentElement.offsetWidth ? $el.style.right = Math.abs($refs.tooltip.getBoundingClientRect().right) - $root.getBoundingClientRect().right - 4 + 'px' : $el.style.right = null;
                                    } )} )"
              >
                <svg
                  class="fill-indigo-500"
                  xmlns="http://www.w3.org/2000/svg"
                  width="17"
                  height="14"
                  aria-hidden="true"
                >
                  <path
                    fill-rule="nonzero"
                    d="M2.014 3.68c.276-1.267.82-2.198 1.629-2.79C4.453.295 5.627 0 7.167 0c.514 0 .908.02 1.185.061L5.035 10.49c-.75 2.494-2.429 3.66-5.035 3.496L2.014 3.68Zm8.648 0c.237-1.227.77-2.147 1.6-2.76C13.09.307 14.274 0 15.814 0c.514 0 .909.02 1.185.061L13.683 10.49c-.79 2.494-2.468 3.66-5.035 3.496L10.662 3.68Z"
                  ></path>
                </svg>
                <p>
                  This component is AWESOME. The hover feature is well-thought-out. Even the smaller details, like using
                  colors, really helps everything stay organized. Cruip is amazing and I really enjoy using it.
                </p>
                <p>
                  Mary Smith <span class="text-slate-600">-</span>
                  <span class="text-slate-400">Software Engineer</span>
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
    <!-- End: Testimonials with Tooltip -->
  </div>
</main>
