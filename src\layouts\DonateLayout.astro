---
import Layout from '~/layouts/PageLayout.astro';

import { getLangFromUrl, useTranslations } from '~/i18n/utils';

const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);

const metadata = {
  title: t('donatePage.metadata.title'),
  description: t('donatePage.metadata.description'),
};
---

<Layout metadata={metadata}>
  <div class="flex justify-center py-4 bg-white">
    <iframe
      id="kofiframe"
      src="https://ko-fi.com/livegrancanaria/?hidefeed=true&widget=true&embed=true&preview=true"
      style="border:none;width:100%;padding:4px;background-color: transparent;"
      height="712"
      title="livegrancanaria"></iframe>
  </div>
</Layout>
