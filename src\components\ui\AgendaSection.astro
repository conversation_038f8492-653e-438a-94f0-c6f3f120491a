---
import WeeklyEvents from '../widgets/WeeklyEvents.astro';
import { getLangFromUrl, useTranslations, useTranslatedPath } from '~/i18n/utils';

const lang = getLangFromUrl(Astro.url);
const translatePath = useTranslatedPath(lang);
const t = useTranslations(lang);

interface Props {
  title: string;
  events: any[];
  pathPrefix: string;
  linkSection: string;
  bgClass: string;
}

const { title, events, pathPrefix, linkSection, bgClass } = Astro.props;
---

<div class={`p-6 ${bgClass}`}>
  <div class="mb-4 flex justify-between items-center">
    <h3 class="text-2xl font-semibold">{title}</h3>
    <a href={linkSection} class="text-muted">{`👉 ${t('viewAll')}`}</a>
  </div>
  <WeeklyEvents events={events} pathPrefix={pathPrefix} />
</div>
