---
import Layout from '~/layouts/LandingLayout.astro';

import Hero2 from '~/components/widgets/Hero2.astro';
import CallToAction from '~/components/widgets/CallToAction.astro';

const metadata = {
  title: 'Subscription Landing Page Demo',
};
---

<Layout metadata={metadata}>
  <!-- Hero2 Widget ******************* -->

  <Hero2
    tagline="Subscription Landing Demo"
    title="Subscription Landing Page: Turn Casual Browsers into Loyal Subscribers!"
    subtitle="Unlock the formula for a Subscription Landing Page that keeps your audience coming back for more."
    callToAction={{ text: 'Call to Action', href: '#', icon: 'tabler:square-rounded-arrow-right' }}
    callToAction2={{ text: 'Learn more', href: '#' }}
    image={{
      src: 'https://images.unsplash.com/photo-1593510987046-1f8fcfc512a0?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
      alt: 'Ironic image associated with canceling a subscription. Subscription Landing Page Demo',
    }}
  />

  <CallToAction
    title="Coming soon"
    subtitle="We are working on the content of these demo pages. You will see them very soon. Stay tuned Stay tuned!"
    callToAction={{
      text: 'Get template',
      href: 'https://github.com/onwidget/astrowind',
      icon: 'tabler:download',
    }}
  />
</Layout>
