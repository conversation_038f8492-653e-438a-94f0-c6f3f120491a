---
import { Icon } from 'astro-icon/components';

export interface Props {
  label?: string;
  class?: string;
  iconClass?: string;
  iconName?: string;
}

const {
  label = 'Toggle Menu',
  class:
    className = 'ml-1.5 text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800 focus:outline-none focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 rounded-lg text-sm p-2.5 inline-flex items-center transition',
  iconClass = 'w-6 h-6',
  iconName = 'tabler:menu',
} = Astro.props;
---

<button type="button" class={className} aria-label={label} data-aw-toggle-menu>
  <Icon name={iconName} class={iconClass} />
</button>
