<!-- <PERSON><PERSON> by <PERSON>rmsF<PERSON> https://www.TermsFeed.com -->
<script
  type="text/javascript"
  charset="UTF-8"
  src="https://www.termsfeed.com/public/cookie-consent/4.1.0/cookie-consent.js"></script>

<script type="text/javascript" charset="UTF-8">
  document.addEventListener('DOMContentLoaded', function () {
    cookieconsent.run({
      notice_banner_type: 'simple',
      consent_type: 'express',
      palette: 'dark',
      language: 'en',
      page_load_consent_levels: ['strictly-necessary'],
      notice_banner_reject_button_hide: false,
      preferences_center_close_button_hide: false,
      page_refresh_confirmation_buttons: false,
      website_name: 'LiveGranCanaria.com',
      website_privacy_policy_url: '/privacy',
    });
  });
</script>

<!-- Google analytics --><!-- Google tag (gtag.js) -->
<script
  type="text/plain"
  data-cookie-consent="tracking"
  async
  src="https://www.googletagmanager.com/gtag/js?id=G-1G6D9KZS4F"></script>

<script type="text/plain" data-cookie-consent="tracking">
  window.dataLayer = window.dataLayer || [];
  function gtag() { dataLayer.push(arguments); }
  gtag('js', new Date());
  gtag('config', 'G-1G6D9KZS4F');
</script>
<!-- end of Google analytics--><!-- <noscript>Free cookie consent management tool by <a href="https://www.termsfeed.com/">TermsFeed</a></noscript> --><!-- End Cookie Consent by TermsFeed https://www.TermsFeed.com --><!-- Below is the link that users can use to open Preferences Center to change their preferences. Do not modify the ID parameter. Place it where appropriate, style it as needed. --><!-- <a href="#" id="open_preferences_center">Update cookies preferences</a> -->
