---
import Layout from '~/layouts/PageLayout.astro';
import CallToAction from '~/components/widgets/CallToAction.astro';
import EventBody from '~/components/dancing/EventBody.astro';
import OtherDancingEvents from '~/components/dancing/OtherDancingEvents.astro';
import TopNavLinks from '~/components/ui/TopNavLinks.astro';
import DayFooter from '~/components/dancing/DayFooter.astro';
import WeeklyBar from '~/components/dancing/WeeklyBar.astro';
import FollowSectionSocials from '~/components/common/FollowSectionSocials.astro';
import { prepareEventData } from '~/utils/eventUtils';

export interface Props {
  dayOfTheWeek: number;
  event: any;
  eventType: 'dancing' | 'socialization';
  lang: string;
  t: Function;
  translatePath: Function;
  url: URL;
  // Opzioni di personalizzazione aggiuntive
  options?: {
    // Consente di sovrascrivere il percorso del WeeklyBar
    weekBarPathPrefix?: string;
    // Consente di personalizzare il componente CallToAction quando non ci sono eventi
    noEventsAlertCustom?: boolean;
    // Consente di disabilitare specifiche sezioni del layout
    disableFollowSection?: boolean;
    disableCallToAction?: boolean;
  };
}

const { dayOfTheWeek, event, eventType, lang, t, translatePath, url, options = {} } = Astro.props;

// Ottieni le opzioni con valori predefiniti
const {
  weekBarPathPrefix,
  noEventsAlertCustom = false,
  disableFollowSection = false,
  disableCallToAction = false,
} = options;

// Deriva alcune costanti in base al tipo di evento
const isLatinEvent = eventType === 'dancing';
const pagePrefix = isLatinEvent ? 'latinDancingDayPage' : 'socialDayPage';
const agendaType = isLatinEvent ? 'dancing' : 'socialization';
const pathPrefix = `${agendaType}/agenda`;
const weekBarPath = weekBarPathPrefix || pathPrefix;

// Prepara i dati con l'utility condivisa
const { pageTitle, metadata, hasOfficialEvent, hasUnofficialEvents, officialFlyer, structuredData, thisPagefullUrl } =
  await prepareEventData(event, lang, url.pathname, t, isLatinEvent ? 'dancing' : 'social');

// Controlla se qualsiasi slot è stato fornito
const hasBeforeMainSlot = Astro.slots.has('before-main');
const hasAfterEventBodySlot = Astro.slots.has('after-event-body');
const hasBeforeFooterSlot = Astro.slots.has('before-footer');
const hasAfterFooterSlot = Astro.slots.has('after-footer');
const hasCustomEventBodySlot = Astro.slots.has('custom-event-body');
const hasCustomNoEventsSlot = Astro.slots.has('custom-no-events-alert');
---

<Layout metadata={metadata} noDonate>
  {
    hasOfficialEvent && structuredData && (
      <>
        <script type="application/ld+json" set:html={JSON.stringify(structuredData)} />
        <script src="https://cdn.jsdelivr.net/npm/add-to-calendar-button@2" async defer />
      </>
    )
  }

  <WeeklyBar pathPrefix={weekBarPath} dayOfTheWeek={event?.day_of_the_week} />

  <main class="py-8 sm:py-16 lg:py-20 mx-auto">
    {/* Slot before-main per inserire contenuto prima del corpo principale */}
    {hasBeforeMainSlot && <slot name="before-main" />}

    {/* Rendering del corpo dell'evento */}
    {
      hasOfficialEvent &&
        officialFlyer &&
        (hasCustomEventBodySlot ? (
          <slot name="custom-event-body" officialFlyer={officialFlyer} pageTitle={pageTitle} />
        ) : (
          <EventBody event={event} eventTitle={pageTitle} flyer={officialFlyer}>
            <DayFooter pageTitle={metadata.title} fullUrl={thisPagefullUrl} event={event} slot="after-event-details" />
          </EventBody>
        ))
    }

    {/* Slot after-event-body per inserire contenuto dopo il corpo dell'evento */}
    {hasAfterEventBodySlot && <slot name="after-event-body" />}

    <div class="text-muted text-xs mx-auto max-w-3xl px-6 sm:px-6">
      {
        !hasOfficialEvent && pageTitle && (
          <h1 class="uppercase text-center px-4 sm:px-6 max-w-3xl mx-auto text-4xl md:text-5xl font-bold leading-tighter tracking-tighter font-heading">
            {pageTitle}
          </h1>
        )
      }

      {/* Slot before-footer per inserire contenuto prima del footer */}
      {hasBeforeFooterSlot && <slot name="before-footer" />}

      {
        hasOfficialEvent || hasUnofficialEvents ? (
          <DayFooter pageTitle={metadata.title} fullUrl={thisPagefullUrl} event={event} />
        ) : hasCustomNoEventsSlot || noEventsAlertCustom ? (
          <slot name="custom-no-events-alert" />
        ) : (
          <CallToAction
            title={t(`${pagePrefix}.noEventsAlert.title`)}
            subtitle={t(`${pagePrefix}.noEventsAlert.subtitle`)}
          />
        )
      }

      {hasUnofficialEvents && <OtherDancingEvents flyers={event?.other_events} />}

      <nav class="my-8">
        <TopNavLinks
          leftHref={translatePath(`/${agendaType}`)}
          leftText={t(`${pagePrefix}.backBtn`)}
          leftVisible={true}
          rightHref={translatePath(`/${agendaType}`)}
          rightText={t(`${pagePrefix}.nextEventBtn`)}
          rightVisible={false}
        />
      </nav>
    </div>

    {/* Slot after-footer per inserire contenuto dopo il footer */}
    {hasAfterFooterSlot && <slot name="after-footer" />}
  </main>

  {!disableFollowSection && <FollowSectionSocials section={agendaType} ctaText={t('followUs')} />}

  {
    !disableCallToAction && (
      <CallToAction
        title={t(`${pagePrefix}.callToAction.title`)}
        subtitle={t(`${pagePrefix}.callToAction.subtitle`)}
        callToAction={{
          text: t(`${pagePrefix}.callToAction.ctaText`),
          href: translatePath('/contact'),
        }}
      />
    )
  }
</Layout>
