---
import Layout from '~/layouts/LandingLayout.astro';

import Hero2 from '~/components/widgets/Hero2.astro';
import CallToAction from "~/components/widgets/CallToAction.astro"

const metadata = {
  title: 'Click-through Landing Page Demo',
};
---

<Layout metadata={metadata}>
  <!-- Hero2 Widget ******************* -->

  <Hero2
    tagline="Click-through Demo"
    title="Click-through Landing Page: The Perfect Bridge to Conversion!"
    subtitle="Learn how to design a Click-Through Landing Page that seamlessly guides visitors to your main offer."
    callToAction={{ text: 'Call to Action', href: '#', icon: 'tabler:square-rounded-arrow-right' }}
    callToAction2={{ text: 'Learn more', href: '#' }}
    image={{
      src: 'https://images.unsplash.com/photo-1516321497487-e288fb19713f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
      alt: 'Click-through Landing Page Hero Image',
    }}
  />

  <CallToAction
    title="Coming soon"
    subtitle="We are working on the content of these demo pages. You will see them very soon. Stay tuned Stay tuned!"
    callToAction={{
      text: 'Get template',
      href: 'https://github.com/onwidget/astrowind',
      icon: 'tabler:download',
    }}
  />
</Layout>
