---
import { getImage } from 'astro:assets';
import { socialLinks } from '~/data/socialLinks';
import { Icon } from 'astro-icon/components';
import dancingBackground from '/src/assets/images/heroes/socialization.webp';

import { getLangFromUrl, useTranslations, useTranslatedPath } from '~/i18n/utils';

const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);
const translatePath = useTranslatedPath(lang);

const optimizedBackground = await getImage({ src: dancingBackground, format: 'webp' });
const dancingSocialLinks = socialLinks.filter((link) => link.sections.includes('socialization'));
---

<div
  class="w-full dark:bg-gray-500"
  style={`background-image: url(${optimizedBackground.src}); background-position: center center; background-blend-mode: multiply; background-size: cover;`}
>
  <div class="container flex flex-col flex-wrap content-center justify-center p-4 py-20 mx-auto md:p-10">
    <p class="text-center text-base text-blue-200 font-bold tracking-wide uppercase">{t('socializing.hero.tagline')}</p>

    <h1 class="text-5xl antialiased font-semibold leadi text-center text-gray-100">{t('socializing.hero.title')}</h1>
    <p class="pt-2 pb-4 text-xl antialiased text-center text-gray-100">{t('socializing.hero.subtitle')}</p>

    <ul class="flex justify-center mb-4 md:order-1 -ml-2 md:ml-4 md:mb-0 rtl:ml-0 rtl:-mr-2 rtl:md:ml-0 rtl:md:mr-4">
      {
        dancingSocialLinks.map(({ ariaLabel, href, icon }) => (
          <li>
            <a
              class="text-white hover:bg-gray-700 focus:outline-none focus:ring-4 focus:ring-gray-700 rounded-lg text-sm p-2.5 inline-flex items-center"
              target="_blank"
              aria-label={ariaLabel}
              href={href}
            >
              {icon && <Icon name={icon} class="w-5 h-5" />}
            </a>
          </li>
        ))
      }
    </ul>
  </div>
</div>
