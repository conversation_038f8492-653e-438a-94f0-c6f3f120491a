---
import Image from '~/components/common/Image.astro';
// import { Image } from 'astro:assets'; // With this component, images are stored locally
import CTA from '../ui/CTA.astro';

const {
  title = await Astro.slots.render('title'),
  subtitle = await Astro.slots.render('subtitle'),
  tagline,
  content = await Astro.slots.render('content'),
  callToAction = await Astro.slots.render('callToAction'),
  callToAction2 = await Astro.slots.render('callToAction2'),
  image = await Astro.slots.render('image'),
} = Astro.props;
---

<section class="relative md:-mt-[76px] not-prose">
  <div class="absolute inset-0 pointer-events-none" aria-hidden="true"></div>
  <div class="relative max-w-7xl mx-auto px-4 sm:px-6">
    <div class="pt-0 md:pt-[76px] pointer-events-none"></div>
    <div class="py-12 md:py-20">
      <div class="text-center pb-10 md:pb-16 max-w-5xl mx-auto">
        {
          tagline && (
            <p
              class="text-base text-secondary dark:text-blue-200 font-bold tracking-wide uppercase"
              set:html={tagline}
            />
          )
        }
        {
          title && (
            <h1
              class="text-5xl md:text-6xl font-bold leading-tighter tracking-tighter mb-4 font-heading dark:text-gray-200"
              set:html={title}
            />
          )
        }
        <div class="max-w-3xl mx-auto">
          {subtitle && <p class="text-xl text-muted mb-6 dark:text-slate-300" set:html={subtitle} />}
          <div class="max-w-xs sm:max-w-md m-auto flex flex-nowrap flex-col sm:flex-row sm:justify-center gap-4">
            {
              callToAction && (
                <div class="flex w-full sm:w-auto">
                  {typeof callToAction === 'string' ? (
                    <Fragment set:html={callToAction} />
                  ) : (
                    <div class="btn btn-primary sm:mb-0 w-full">
                      <CTA callToAction={callToAction} />
                    </div>
                  )}
                </div>
              )
            }
            {
              callToAction2 && (
                <div class="flex w-full sm:w-auto">
                  {typeof callToAction2 === 'string' ? (
                    <Fragment set:html={callToAction2} />
                  ) : (
                    <div class="btn w-full">
                      <CTA callToAction={callToAction2} />
                    </div>
                  )}
                </div>
              )
            }
          </div>
        </div>
        {content && <Fragment set:html={content} />}
      </div>
      <div>
        {
          image && (
            <div class="relative m-auto max-w-5xl">
              {typeof image === 'string' ? (
                <Fragment set:html={image} />
              ) : (
                <Image
                  class="mx-auto rounded-md w-full"
                  widths={[400, 768, 1024, 2040]}
                  sizes="(max-width: 767px) 400px, (max-width: 1023px) 768px, (max-width: 2039px) 1024px, 2040px"
                  loading="eager"
                  width={1024}
                  height={576}
                  {...image}
                />
              )}
            </div>
          )
        }
      </div>
    </div>
  </div>
</section>
