---
import { Icon } from 'astro-icon/components';

interface Props {
  smoking_allowed: boolean;
  pets_allowed: boolean;
  couples_allowed: boolean;
  t: any; // Translation function
}

const { smoking_allowed, pets_allowed, couples_allowed, t } = Astro.props;
---

<div class="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm">
  <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-6">{t('house_rules') || 'House Rules'}</h2>
  <div class="grid grid-cols-3 gap-4">
    <!-- No Smoking -->
    <div class="flex flex-col items-center text-center">
      <div class="bg-red-600 p-4 rounded-full mb-3">
        <Icon name="tabler:smoking-no" class="w-8 h-8 text-white" />
      </div>
      <span class="text-sm font-medium text-gray-900 dark:text-white">
        {smoking_allowed ? t('smoking_allowed') || 'Smoking Allowed' : t('no_smoking') || 'No Smoking'}
      </span>
    </div>

    <!-- No Pets -->
    <div class="flex flex-col items-center text-center">
      <div class="bg-red-600 p-4 rounded-full mb-3">
        <div class="relative">
          <Icon name="tabler:paw" class="w-8 h-8 text-white" />
          {
            !pets_allowed && (
              <div class="absolute inset-0 flex items-center justify-center">
                <div class="w-10 h-0.5 bg-white absolute rotate-45" />
                <div class="w-10 h-0.5 bg-white absolute -rotate-45" />
              </div>
            )
          }
        </div>
      </div>
      <span class="text-sm font-medium text-gray-900 dark:text-white">
        {pets_allowed ? t('pets_allowed') || 'Pets Allowed' : t('no_pets') || 'No Pets'}
      </span>
    </div>

    <!-- No Couples -->
    <div class="flex flex-col items-center text-center">
      <div class="bg-red-600 p-4 rounded-full mb-3">
        <div class="relative">
          <Icon name="tabler:users" class="w-8 h-8 text-white" />
          {
            !couples_allowed && (
              <div class="absolute inset-0 flex items-center justify-center">
                <div class="w-10 h-0.5 bg-white absolute rotate-45" />
                <div class="w-10 h-0.5 bg-white absolute -rotate-45" />
              </div>
            )
          }
        </div>
      </div>
      <span class="text-sm font-medium text-gray-900 dark:text-white">
        {couples_allowed ? t('couples_allowed') || 'Couples Allowed' : t('no_couples') || 'No Couples'}
      </span>
    </div>
  </div>
</div>
