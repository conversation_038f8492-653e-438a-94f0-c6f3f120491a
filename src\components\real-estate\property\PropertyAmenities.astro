---
import { Icon } from 'astro-icon/components';

interface Props {
  features?: string[]; // Made optional with ?
  t: any; // Translation function
}

const { features = [], t } = Astro.props; // Default empty array if features is undefined
---

<div class="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm">
  <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-6">{t('amenities')}</h2>

  {
    features.length > 0 ? (
      <div class="grid grid-cols-2 md:grid-cols-3 gap-y-4 gap-x-6">
        {features.map((feature) => (
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <Icon name="tabler:check" class="w-5 h-5 text-primary-600 dark:text-primary-400" />
            </div>
            <span class="ml-3 text-gray-700 dark:text-gray-300 capitalize">{t(feature)}</span>
          </div>
        ))}
      </div>
    ) : (
      <p class="text-gray-500 dark:text-gray-400">{t('no_amenities')}</p>
    )
  }
</div>
