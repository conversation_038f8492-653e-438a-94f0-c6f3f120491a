<div id="disqus_thread"></div>

<script is:inline>
  // https://www.webdesign-sopelnik.de/en/blog/adding-comments-to-your-astro-blog-with-disqus/
  const embedJs = 'https://livegrancanaria-com.disqus.com/embed.js';

  if (!window.DisqusWidget) {
    window.DisqusWidget = {
      overwriteGlobalSelectors: function () {
        window.$disqus = document.querySelector('#disqus_thread');
      },
      init: function () {
        this.overwriteGlobalSelectors();
        this.addListeners();
        this.initDisqus();
      },
      //   Enable only if use view transitions
      // addListeners: function () {
      //   // After language switched
      //   document.addEventListener('astro:after-swap', () => {
      //     this.overwriteGlobalSelectors();
      //     this.initDisqus();
      //   });
      // },
      addListeners: function () {
        // After language switched
        this.overwriteGlobalSelectors();
        this.initDisqus();
      },
      initDisqus: () => {
        // early escape if $disqus not exists
        if (window.$disqus === null) {
          return;
        }

        // Reset DISQUS, Rather than loading new embed.js
        if (window.DISQUS) {
          window.DISQUS.reset({
            reload: true,
          });
          return;
        }

        (function () {
          // DON'T EDIT BELOW THIS LINE
          const d = document,
            s = d.createElement('script');
          //   s.src = `[${embedJs}]`;
          s.src = embedJs;
          s.setAttribute('data-timestamp', String(+new Date()));
          (d.head || d.body).appendChild(s);
        })();
      },
    };

    window.DisqusWidget.init();
  }
</script>

<noscript
  >Please enable JavaScript to view the <a href="https://disqus.com/?ref_noscript">comments powered by Disqus.</a
  ></noscript
>
