import path from 'path';
import { fileURLToPath } from 'url';
import { defineConfig } from 'astro/config';
import sitemap from '@astrojs/sitemap';
import tailwind from '@astrojs/tailwind';
import mdx from '@astrojs/mdx';
import partytown from '@astrojs/partytown';
import icon from 'astro-icon';
import tasks from './src/utils/tasks';
import { readingTimeRemarkPlugin } from './src/utils/frontmatter';
import { ANALYTICS, SITE } from './src/utils/config.ts';
import AstroPWA from '@vite-pwa/astro';
// import compress from 'astro-compress';
const __dirname = path.dirname(fileURLToPath(import.meta.url));
const whenExternalScripts = (items = []) => ANALYTICS.vendors.googleAnalytics.id && ANALYTICS.vendors.googleAnalytics.partytown ? Array.isArray(items) ? items.map(item => item()) : [items()] : [];


// https://astro.build/config
export default defineConfig({
  site: SITE.site,
  base: SITE.base,
  trailingSlash: SITE.trailingSlash ? 'always' : 'never',
  output: 'static',
  image: {
    domains: ['v5.airtableusercontent.com'],
    remotePatterns: [{
      protocol: 'https',
      hostname: 'v5.airtableusercontent.com'
    }]
  },
  integrations: [sitemap(), mdx(), tasks(), AstroPWA({
    registerType: 'autoUpdate',
    manifest: {
      name: 'LiveGranCanaria',
      short_name: 'LiveGC',
      description: 'A platform that groups events and resources in Gran Canaria.',
      theme_color: '#3367D6',
      background_color: '#ffffff',
      icons: [
        {
          src: 'pwa-192x192.png',
          sizes: '192x192',
          type: 'image/png'
        },
        {
          src: 'pwa-512x512.png',
          sizes: '512x512',
          type: 'image/png'
        },
        {
          src: 'pwa-512x512.png',
          sizes: '512x512',
          type: 'image/png',
          purpose: 'any maskable'
        }
      ]
    }
  }), tailwind({ applyBaseStyles: false }),
  icon({
    include: {
      tabler: ['*'],
      'flat-color-icons': [
        'template',
        'gallery',
        'approval',
        'document',
        'advertising',
        'currency-exchange',
        'voice-presentation',
        'business-contact',
        'database',
      ],
    },
  }),
  ...whenExternalScripts(() => partytown({
    config: {
      forward: ['dataLayer.push']
    }
  })),
    // compress({
    //   CSS: true,
    //   HTML: {
    //     'html-minifier-terser': {
    //       removeAttributeQuotes: false,
    //     },
    //   },
    //   Image: false,
    //   JavaScript: true,
    //   SVG: false,
    //   Logger: 1,
    // }),
  ],
  compressHTML: false,
  markdown: {
    remarkPlugins: [readingTimeRemarkPlugin]
  },

  vite: {
    resolve: {
      alias: {
        '~': path.resolve(__dirname, './src')
      }
    }
  }
});