---
import { Icon } from 'astro-icon/components';

import { socialLinks } from '~/data/socialLinks';
---

<section>
  <div class="container px-6 py-10 mx-auto">
    <h1 class="text-2xl font-semibold text-center text-gray-800 capitalize lg:text-3xl dark:text-white">
      Join <span class="text-blue-500">Us</span>
    </h1>

    <div class="grid grid-cols-1 gap-8 mt-8 xl:mt-12 xl:gap-16 md:grid-cols-2 xl:grid-cols-3 text-white">
      {
        socialLinks.map((s) => (
          <a
            href={s.href} target="_blank"
            class={`flex flex-col items-center p-6 space-y-3 text-center rounded-xl bg-${s.bgColor}-500`}
          >
            <Icon name={s.icon} class="w-20 h-20" />
            <h1 class="text-xl font-semibold capitalize text-white">{s.aria<PERSON><PERSON><PERSON>}</h1>
          </a>
        ))
      }
    </div>
  </div>
</section>
