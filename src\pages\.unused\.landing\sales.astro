---
import Layout from '~/layouts/LandingLayout.astro';

import Hero2 from '~/components/widgets/Hero2.astro';
import CallToAction from '~/components/widgets/CallToAction.astro';

const metadata = {
  title: 'Sales Landing Page Demo',
};
---

<Layout metadata={metadata}>
  <!-- Hero2 Widget ******************* -->

  <Hero2
    tagline="Long-form Sales Demo"
    title="Long-form Sales: Sell with a Story: The Long-form Way!"
    subtitle="Dive deep into crafting a Landing Page that narrates, persuades, and converts."
    callToAction={{ text: 'Call to Action', href: '#', icon: 'tabler:square-rounded-arrow-right' }}
    callToAction2={{ text: 'Learn more', href: '#' }}
    image={{
      src: 'https://images.unsplash.com/photo-1621452773781-0f992fd1f5cb?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1626&q=80',
      alt: 'Children telling a story. Long-form Sales Landing Page demo',
    }}
  />

  <CallToAction
    title="Coming soon"
    subtitle="We are working on the content of these demo pages. You will see them very soon. Stay tuned Stay tuned!"
    callToAction={{
      text: 'Get template',
      href: 'https://github.com/onwidget/astrowind',
      icon: 'tabler:download',
    }}
  />
</Layout>
