---
import EventDetails from '~/components/dancing/EventDetails.astro';
import EventLastUpdate from '~/components/dancing/EventLastUpdate.astro';
import EventLocationNotes from './EventLocationNotes.astro';
import EventProgram from './EventProgram.astro';
import type { EventInfo } from '~/types';
import EventHeader from './EventHeader.astro';
import { getLangFromUrl, useTranslations } from '~/i18n/utils';
import YoutubeVideo from '../widgets/YoutubeVideo.astro';
import { convertToBoolean } from '~/utils/utils';

interface Props {
  event: EventInfo;
  eventTitle: string;
  flyer: string;
}

const { event, eventTitle, flyer } = Astro.props;

const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);

const isNotEmpty = (data: any[]): boolean => data?.length > 0 && data[0] !== null;

const hasWorkshopAndSocial =
  event?.start_workshop && event?.end_workshop && event?.start_social && event?.end_social && event?.workshop;
---

<article>
  <header>
    <EventHeader name={eventTitle} excerpt={t(event?.excerpt_t)} flyerUrl={flyer || ''} />
  </header>

  <div
    class="mx-auto px-6 sm:px-6 max-w-3xl prose prose-lg lg:prose-xl dark:prose-invert dark:prose-headings:text-slate-300 prose-md prose-headings:font-heading prose-headings:leading-tighter prose-headings:tracking-tighter prose-headings:font-bold prose-a:text-primary dark:prose-a:text-blue-400 prose-img:rounded-md prose-img:shadow-lg mt-8 prose-headings:scroll-mt-[80px]"
  >
    <div class="flex">
      <EventDetails
        datetimeStart={event?.start}
        datetimeEnd={event?.end}
        locationName={event?.location_name}
        locationAddress={event?.location_address}
        locationLinkMap={event?.location_link_map}
        cost={event?.cost}
        costDetails={t(event?.cost_details)}
        contact={event?.contact}
        contactType={event?.contact_type}
        childrenFriendly={convertToBoolean(event?.children_friendly)}
      />
    </div>

    <div class="border-t border-gray-600">
      {event?.intro && <p set:html={t(event.intro)} />}

      {
        hasWorkshopAndSocial && (
          <EventProgram
            timeStartWorkshop={event.start_workshop}
            timeEndWorkshop={event.end_workshop}
            timeStartSocial={event.start_social}
            timeEndSocial={event.end_social}
            socialDances={event.dances}
            workshop={event.workshop}
            instructors={event.workshop_instructors}
          />
        )
      }

      {event?.outro && <p set:html={t(event.outro)} />}

      <div class="text-muted text-xs border-t border-gray-600">
        <slot name="after-event-details" />
      </div>

      {
        event?.updatedAt && (
          <div class="border-t border-gray-600">
            <EventLastUpdate updatedAt={event.updatedAt} />
          </div>
        )
      }

      {event?.location_youtube_video_id && <YoutubeVideo id={event.location_youtube_video_id} />}
      {isNotEmpty(event?.location_notes) && <EventLocationNotes locationNotes={event.location_notes} />}
    </div>

    {event?.location_iframe_map && <div class="my-4" set:html={event.location_iframe_map} />}
  </div>
</article>
