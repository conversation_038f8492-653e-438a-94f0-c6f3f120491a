---
import PropertyPage from '~/components/real-estate/PropertyPage.astro';
import { getLangFromUrl } from '~/i18n/utils';
import { getPropertyPaths } from '~/utils/property-page-utils';
import { properties } from '~/connectors/airtable';

const lang = getLangFromUrl(Astro.url);

// Utilizza la funzione comune per generare i percorsi statici
export function getStaticPaths() {
  return getPropertyPaths();
}

const { property } = Astro.params;
---

<PropertyPage propertyName={property} lang={lang} />
