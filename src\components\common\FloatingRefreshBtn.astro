---
// https://tailwindcomponents.com/component/floating-button
import { Icon } from 'astro-icon/components';
---

<!-- component -->
<div class="group fixed bottom-0 right-0 p-2 flex items-end justify-end w-24 h-24" style="z-index: 999;">
  <div
    id="refresh-btn"
    class="cursor-pointer text-white shadow-xl flex items-center justify-center p-3 rounded-full bg-primary z-100 absolute"
  >
    <Icon name="tabler:refresh" width={24} height={24} class="inline-block" />
  </div>
</div>

<script>
  const button = document.getElementById('refresh-btn');
  if (button) button.addEventListener('click', () => location.reload(true));
</script>
