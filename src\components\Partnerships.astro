---
import Image from '~/components/common/Image.astro';

export interface Props {
  title: string;
  disclaimer: string;
}

const { title, disclaimer } = Astro.props;
---

<section class="bg-white dark:bg-gray-900">
  <div class="py-8 lg:py-16 mx-auto max-w-screen-xl px-4">
    <div class="mb-8 lg:mb-16">
      <h2
        class="text-3xl font-extrabold tracking-tight leading-tight text-center text-gray-900 dark:text-white md:text-4xl"
      >
        {title}
      </h2>
      <div class="text-center text-muted">
        <small class="text-center">{disclaimer}</small>
      </div>
    </div>

    <div class="grid grid-cols-2 gap-8 text-gray-500 sm:gap-12 md:grid-cols-6 lg:grid-cols-6 dark:text-gray-400">
      <a href="#" class="flex justify-center items-center">
        <Image
          src="~/assets/images/partners/occidental.png"
          class="w-full md:h-full"
          widths={[180]}
          width={180}
          sizes="(max-width: 180px) 180px, 180px"
          alt="Occidental Las Palmas Hotel"
          aspectRatio="1:1"
          layout="cover"
          loading="lazy"
          decoding="async"
        />
      </a>
      <a href="#" class="flex justify-center items-center">
        <Image
          src="~/assets/images/partners/bex.png"
          class="w-full md:h-full"
          widths={[180]}
          width={180}
          sizes="(max-width: 180px) 180px, 180px"
          alt="Bex Hotel - Las Palmas"
          aspectRatio="1:1"
          layout="cover"
          loading="lazy"
          decoding="async"
        />
      </a>
      <a href="#" class="flex justify-center items-center">
        <Image
          src="~/assets/images/partners/alboroto.png"
          class="w-full md:h-full"
          widths={[180]}
          width={180}
          sizes="(max-width: 180px) 180px, 180px"
          alt="Little Buddha"
          aspectRatio="1:1"
          layout="cover"
          loading="lazy"
          decoding="async"
        />
      </a>
      <a href="#" class="flex justify-center items-center">
        <Image
          src="~/assets/images/partners/elder.png"
          class="w-full md:h-full"
          widths={[180]}
          width={180}
          sizes="(max-width: 180px) 180px, 180px"
          alt="Restaurante Terraza Elder"
          aspectRatio="1:1"
          layout="cover"
          loading="lazy"
          decoding="async"
        />
      </a>
      <a href="#" class="flex justify-center items-center">
        <Image
          src="~/assets/images/partners/lerose.png"
          class="w-full md:h-full"
          widths={[180]}
          width={180}
          sizes="(max-width: 180px) 180px, 180px"
          alt="Le Rose"
          aspectRatio="1:1"
          layout="cover"
          loading="lazy"
          decoding="async"
        />
      </a>
      <a href="#" class="flex justify-center items-center">
        <Image
          src="~/assets/images/partners/tucandela.png"
          class="w-full md:h-full"
          widths={[180]}
          width={180}
          sizes="(max-width: 180px) 180px, 180px"
          alt="El Altar"
          aspectRatio="1:1"
          layout="cover"
          loading="lazy"
          decoding="async"
        />
      </a>
      <a href="#" class="flex justify-center items-center">
        <Image
          src="~/assets/images/partners/l_buddha.png"
          class="w-full md:h-full"
          widths={[180]}
          width={180}
          sizes="(max-width: 180px) 180px, 180px"
          alt="Little Buddha"
          aspectRatio="1:1"
          layout="cover"
          loading="lazy"
          decoding="async"
        />
      </a>
    </div>
  </div>
</section>
