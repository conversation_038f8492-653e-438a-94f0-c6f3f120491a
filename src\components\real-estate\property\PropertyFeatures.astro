---
import { Icon } from 'astro-icon/components';

interface Props {
  bedrooms: number;
  bathrooms: number;
  area: number;
  t: any; // Translation function
}

const { bedrooms, bathrooms, area, t } = Astro.props;
---

<div class="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm">
  <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-6">{t('features')}</h2>
  <div class="grid grid-cols-3 gap-8">
    <div class="flex flex-col items-center text-center">
      <div class="bg-primary-50 dark:bg-gray-700 p-3 rounded-full mb-3">
        <Icon name="tabler:bed" class="w-8 h-8 text-primary-600 dark:text-primary-400" />
      </div>
      <span class="text-lg font-semibold text-gray-900 dark:text-white">{bedrooms}</span>
      <span class="text-sm text-gray-600 dark:text-gray-400">{t('bedrooms')}</span>
    </div>
    <div class="flex flex-col items-center text-center">
      <div class="bg-primary-50 dark:bg-gray-700 p-3 rounded-full mb-3">
        <Icon name="tabler:bath" class="w-8 h-8 text-primary-600 dark:text-primary-400" />
      </div>
      <span class="text-lg font-semibold text-gray-900 dark:text-white">{bathrooms}</span>
      <span class="text-sm text-gray-600 dark:text-gray-400">{t('bathrooms')}</span>
    </div>
    <div class="flex flex-col items-center text-center">
      <div class="bg-primary-50 dark:bg-gray-700 p-3 rounded-full mb-3">
        <Icon name="tabler:ruler-measure" class="w-8 h-8 text-primary-600 dark:text-primary-400" />
      </div>
      <span class="text-lg font-semibold text-gray-900 dark:text-white">{area} m²</span>
      <span class="text-sm text-gray-600 dark:text-gray-400">{t('area')}</span>
    </div>
  </div>
</div>
