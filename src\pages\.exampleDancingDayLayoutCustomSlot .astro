---
import EventDayLayout from '~/layouts/EventDayLayout.astro';
import { latinEvents } from '~/connectors/airtable';
import { getLangFromUrl, useTranslations, useTranslatedPath } from '~/i18n/utils';
import { YouMayAlsoWantLatinDay } from '~/components/dancing';
import CustomEventIntro from '~/components/dancing/CustomEventIntro.astro';
import CustomPromotionBanner from '~/components/dancing/CustomPromotionBanner.astro';
import SpecialEventBody from '~/components/dancing/SpecialEventBody.astro';

export interface Props {
  dayOfTheWeek: number;
}

const { dayOfTheWeek } = Astro.props;

// i18n setup
const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);
const translatePath = useTranslatedPath(lang);

// Data fetching
const event = latinEvents.find((e) => e.day_of_the_week === dayOfTheWeek);
if (!event) {
  throw new Error(`No event found for day ${dayOfTheWeek}`);
}

// Check if this is a special event that needs custom treatment
const isSpecialEvent = event.is_special === true;
---

<EventDayLayout
  dayOfTheWeek={dayOfTheWeek}
  event={event}
  eventType="dancing"
  lang={lang}
  t={t}
  translatePath={translatePath}
  url={Astro.url}
  options={{
    // Opzionalmente personalizza il percorso del WeeklyBar
    weekBarPathPrefix: 'dancing/special-agenda',
  }}
>
  <!-- Slot per aggiungere contenuto prima del corpo principale -->
  <CustomEventIntro event={event} slot="before-main" />

  <!-- Utilizza un corpo dell'evento completamente personalizzato se necessario -->
  {isSpecialEvent && <SpecialEventBody event={event} slot="custom-event-body" />}

  <!-- Aggiungi contenuto dopo il corpo dell'evento -->
  <CustomPromotionBanner slot="after-event-body" />

  <!-- Mantieni i componenti specifici per gli eventi di ballo latino -->
  <YouMayAlsoWantLatinDay
    title={t('latinDancingDayPage.YouMayAlsoWant.title')}
    items={t('latinDancingDayPage.YouMayAlsoWant.items')}
    slot="after-footer"
  />
</EventDayLayout>
