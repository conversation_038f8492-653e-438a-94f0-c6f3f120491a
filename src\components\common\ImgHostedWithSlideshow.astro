---
import { getImage } from 'astro:assets';

interface Props {
  flyerUrl: string;
  index: number;
  length: number;
  prefix?: string;
  width?: string;
  height?: string;
  className?: string;
}

const { flyerUrl, index, length, prefix = 'flyer', width = 'w-full', height = 'h-full', className = '' } = Astro.props;

const identifier = `${prefix}-${index}`;

const optimizedFlyer = await getImage({ src: flyerUrl, format: 'webp', width: 1080, height: 1080 });
---

<!-- Thumbnail -->
<a href={`#${identifier}`} class={className}>
  <img
    src={optimizedFlyer?.src}
    class={`rounded shadow-sm min-h-48 dark:bg-gray-500 aspect-square hover:opacity-90 transition-opacity duration-300 ${width} ${height}`}
    alt={`${prefix} ${index}`}
    loading="eager"
    decoding="async"
  />
</a>

<!-- Lightbox -->
<div id={identifier} class="lightbox">
  <div class="fixed inset-0 z-[99] flex items-center justify-center bg-black/80 backdrop-blur-sm">
    <div class="relative flex items-center justify-center w-11/12 xl:w-4/5 h-11/12">
      <!-- Loading Spinner -->
      <div id={`${identifier}-loading`} class="absolute inset-0 flex items-center justify-center z-10 hidden">
        <div class="w-12 h-12 border-4 border-white border-t-transparent rounded-full animate-spin"></div>
      </div>

      <!-- Main Image -->
      <img
        src={optimizedFlyer?.src}
        class="object-contain object-center w-full h-full select-none max-h-[90vh] transition-opacity duration-300"
        alt="Flyer fullsize view"
      />

      <!-- Navigation Buttons -->
      {
        index > 0 && (
          <a
            href={`#${prefix}-${index - 1}`}
            class="absolute left-0 flex items-center justify-center text-white translate-x-10 rounded-full cursor-pointer xl:-translate-x-24 2xl:-translate-x-32 bg-black/50 w-14 h-14 hover:bg-white/20 transition-colors duration-300 hover:ring hover:ring-white"
          >
            <svg
              class="w-6 h-6"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke-width="1.5"
              stroke="currentColor"
            >
              <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 19.5L8.25 12l7.5-7.5" />
            </svg>
          </a>
        )
      }

      {
        index + 1 < length && (
          <a
            href={`#${prefix}-${index + 1}`}
            class="absolute right-0 flex items-center justify-center text-white -translate-x-10 rounded-full cursor-pointer xl:translate-x-24 2xl:translate-x-32 bg-black/50 w-14 h-14 hover:bg-white/20 transition-colors duration-300 hover:ring hover:ring-white"
          >
            <svg
              class="w-6 h-6"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke-width="1.5"
              stroke="currentColor"
            >
              <path stroke-linecap="round" stroke-linejoin="round" d="M8.25 4.5l7.5 7.5-7.5 7.5" />
            </svg>
          </a>
        )
      }

      <!-- Close Button -->
      <a
        href="#_"
        class="absolute top-0 right-0 flex items-center justify-center text-white rounded-full cursor-pointer bg-black/50 w-14 h-14 hover:bg-white/20 transition-colors duration-300 hover:ring hover:ring-white"
      >
        <svg
          class="w-6 h-6"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          stroke-width="1.5"
          stroke="currentColor"
        >
          <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12"></path>
        </svg>  
      </a>

      <!-- Image Counter -->
      <div
        class="absolute bottom-2 left-1/2 transform -translate-x-1/2 bg-black/50 text-white px-4 py-2 rounded-full text-sm"
      >
        <span>{index + 1} / {length}</span>
      </div>
    </div>
  </div>
</div>

<style>
  .lightbox {
    display: none;
  }

  .lightbox:target {
    display: block;
  }
</style>

<script>
  function setupLightbox(prefix, index, length) {
    const lightbox = document.getElementById(`${prefix}-${index}`);
    if (!lightbox) return;

    const loadingSpinner = document.getElementById(`${prefix}-${index}-loading`);
    const mainImage = lightbox.querySelector('img');
    const prevButton = lightbox.querySelector('a[href^="#"][href$="-' + (index - 1) + '"]');
    const nextButton = lightbox.querySelector('a[href^="#"][href$="-' + (index + 1) + '"]');

    async function preloadImage(src) {
      loadingSpinner.classList.remove('hidden');
      mainImage.classList.add('opacity-50');

      try {
        const img = new Image();
        await new Promise((resolve, reject) => {
          img.onload = resolve;
          img.onerror = reject;
          img.src = src;
        });
      } finally {
        loadingSpinner.classList.add('hidden');
        mainImage.classList.remove('opacity-50');
      }
    }

    function handleNavigation(e, targetIndex) {
      if (targetIndex < 0 || targetIndex >= length) {
        e.preventDefault();
        return;
      }

      const targetImg = document.querySelector(`#${prefix}-${targetIndex} img`);
      if (targetImg) {
        e.preventDefault();
        preloadImage(targetImg.src).then(() => {
          window.location.hash = `${prefix}-${targetIndex}`;
        });
      }
    }

    if (prevButton) {
      prevButton.addEventListener('click', (e) => handleNavigation(e, index - 1));
    }

    if (nextButton) {
      nextButton.addEventListener('click', (e) => handleNavigation(e, index + 1));
    }
  }

  // Set up lightboxes for all flyers
  document.addEventListener('DOMContentLoaded', () => {
    const flyers = document.querySelectorAll('[id^="flyer-"]');
    flyers.forEach((flyer) => {
      const [prefix, indexStr] = flyer.id.split('-');
      const index = parseInt(indexStr, 10);
      setupLightbox(prefix, index, flyers.length);
    });
  });
</script>
