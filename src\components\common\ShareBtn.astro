---
import { Icon } from 'astro-icon/components';
---

<div class="relative">
  <button
    class="sharebtn relative flex z-10 border p-2 opacity-50 hover:opacity-100 focus:outline-none font-bold text-gray-400 dark:text-slate-300 hover:text-black dark:hover:text-slate-200"
    title="click to enable menu"
  >
    <span class="inline-block pr-4">Share</span>
    <Icon name="tabler:share" class="mr-2 w-5 h-5" />
  </button>

  <div
    class="sharebtn-dropdown absolute right-0 mt-0 w-48 bg-white rounded-sm overflow-hidden shadow-lg z-20 hidden border border-gray-100"
  >
    <a href="/instagram" title="Share on Instagram" class="flex px-4 py-2 text-sm text-gray-800 border-b hover:bg-blue-100">
      <Icon
        name="tabler:brand-instagram"
        class="mr-2 w-5 h-5 text-gray-400 dark:text-slate-500 hover:text-black dark:hover:text-slate-300"
      />
      <span class="text-gray-600">Instagram</span>
    </a>

    <a href="#" title="Share on Facebook" class="flex px-4 py-2 text-sm text-gray-800 border-b hover:bg-blue-100">
      <Icon
        name="tabler:brand-facebook"
        class="mr-2 w-5 h-5 text-gray-400 dark:text-slate-500 hover:text-black dark:hover:text-slate-300"
      />
      <span class="text-gray-600">Facebook</span>
    </a>

    <a href="#" title="Share on Whatsapp" class="flex px-4 py-2 text-sm text-gray-800 border-b hover:bg-blue-100">
      <Icon
        name="tabler:brand-whatsapp"
        class="mr-2 w-5 h-5 text-gray-400 dark:text-slate-500 hover:text-black dark:hover:text-slate-300"
      />
      <span class="text-gray-600">Whatsapp</span>
    </a>

    <a
      href="mailto:<EMAIL>?subject=Oggetto%20della%20mail&body=Testo%20della%20mail"
      title="Send via mail"
      class="flex px-4 py-2 text-sm text-gray-800 border-b hover:bg-blue-100"
    >
      <Icon
        name="tabler:mail"
        class="mr-2 w-5 h-5 text-gray-400 dark:text-slate-500 hover:text-black dark:hover:text-slate-300"
      />
      <span class="text-gray-600">Mail</span>
    </a>
  </div>
</div>

<style lang="postcss">
.sharebtn {
  /* @apply relative flex z-10 bg-white border rounded-md p-2 opacity-50 hover:opacity-100 focus:outline-none focus:border-blue-400; */
}

.sharebtn:hover + .sharebtn-dropdown,
.sharebtn:focus + .sharebtn-dropdown {
  display: block;
}

.sharebtn-dropdown {
  /* @apply absolute right-0 mt-0 w-48 bg-white rounded-sm overflow-hidden shadow-lg z-20 hidden border border-gray-100; */
}
</style>
