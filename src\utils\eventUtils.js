import { getImage } from 'astro:assets';

/**
 * Prepares event data including flyer selection, image processing, and metadata
 * 
 * @param {Object} event The event object from Airtable
 * @param {string} lang Current language
 * @param {string} pageUrl Current page URL
 * @param {Function} t Translation function
 * @param {string} agendaType Type of agenda ('dancing' or 'social')
 * @returns {Object} Processed event data and metadata
 */
export async function prepareEventData(event, lang, pageUrl, t, agendaType) {
    const isLatinEvent = agendaType === 'dancing';
    const pagePrefix = isLatinEvent ? 'latinDancingDayPage' : 'socialDayPage';
    const agendaText = isLatinEvent ? 'dancingAgenda' : 'socialAgenda';

    const thisPagefullUrl = new URL(pageUrl, import.meta.env.DOMAIN).toString();

    // Flyer handling
    const hasFlyerEn = Array.isArray(event?.flyer) && event.flyer.length > 0;
    const hasFlyerEs = Array.isArray(event?.flyer_es) && event.flyer_es.length > 0;
    const hasOfficialEvent = hasFlyerEn || hasFlyerEs;
    const hasUnofficialEvents = Array.isArray(event?.other_events) && event.other_events.length > 0;

    // Process images
    let ogImage;
    let officialFlyer;
    let structuredData;

    // Select appropriate flyer based on language
    let flyerToUse;
    if (hasOfficialEvent) {
        if (lang === 'es' && hasFlyerEs && event.flyer_es[0]) {
            flyerToUse = event.flyer_es[0];
        } else if (lang === 'es' && hasFlyerEn && event.flyer[0]) {
            flyerToUse = event.flyer[0];
        } else if (lang !== 'es' && !hasFlyerEn && hasFlyerEs && event.flyer_es[0]) {
            flyerToUse = event.flyer_es[0];
        } else if (hasFlyerEn && event.flyer[0]) {
            flyerToUse = event.flyer[0];
        }
    }

    // Process flyerToUse and generate structured data
    if (flyerToUse) {
        try {
            ogImage = await getImage({
                src: flyerToUse.url,
                width: 1200,
                height: 630,
                format: 'webp',
            });

            officialFlyer = await getImage({
                src: flyerToUse.url,
                width: 1080,
                height: 1080,
                format: 'webp',
            });

            structuredData = generateStructuredData(event, ogImage, thisPagefullUrl, hasOfficialEvent, t, pagePrefix);
        } catch (error) {
            console.error('Error processing event images:', error);
        }
    } else if (event?.og_image?.[0]?.url) {
        try {
            ogImage = await getImage({
                src: event.og_image[0].url,
                width: 1200,
                height: 630,
                format: 'webp',
            });
        } catch (error) {
            console.error('Error processing og image:', error);
        }
    }

    // Page title and metadata
    const pageTitle = hasOfficialEvent && event?.name ? t(event.name) : event?.slug ? t(event.slug) : '';
    const metadata = {
        title: pageTitle ? `${pageTitle} - ${t(agendaText)}` : t(agendaText),
        description: event?.slug
            ? t(
                `${pagePrefix}.metadata.description.${event.slug.toLowerCase()}`,
                t(`${pagePrefix}.metadata.description.default`)
            )
            : t(`${pagePrefix}.metadata.description.default`),
        openGraph: {
            images: ogImage
                ? [
                    {
                        url: ogImage.src,
                        width: ogImage.attributes.width,
                        height: ogImage.attributes.height,
                        type: 'image/webp',
                        alt: pageTitle ? `${pageTitle} - ${t(agendaText)}` : t(agendaText),
                    },
                ]
                : [],
        },
    };

    return {
        event,
        pageTitle,
        metadata,
        hasOfficialEvent,
        hasUnofficialEvents,
        officialFlyer,
        structuredData,
        thisPagefullUrl
    };
}

/**
 * Generates structured data for an event in schema.org format
 */
function generateStructuredData(event, ogImage, pageUrl, hasOfficialEvent, t, pagePrefix) {
    if (!event?.name || !event?.start) return null;

    // Format date-time from date string and time in HH.MM format
    const formatDateTime = (dateString, timeString) => {
        if (!dateString || !timeString) return '';

        try {
            const baseDate = new Date(dateString);
            if (isNaN(baseDate.getTime())) return '';

            const [hours, minutes] = timeString.split('.').map((num) => parseInt(num, 10));
            if (isNaN(hours) || isNaN(minutes)) return '';

            const fullDateTime = new Date(baseDate);
            fullDateTime.setHours(hours, minutes, 0, 0);
            return fullDateTime.toISOString();
        } catch (e) {
            console.error('Error formatting date time:', e);
            return '';
        }
    };

    const formatPrice = (price) => {
        if (price === undefined || price === null) return '0';
        const numPrice = Number(price);
        return isNaN(numPrice) ? '0' : numPrice.toString();
    };

    // Calculate event times
    const startDate = event.start;
    const workshopStart = formatDateTime(startDate, event.start_workshop);
    const workshopEnd = formatDateTime(startDate, event.end_workshop);
    const socialStart = formatDateTime(startDate, event.start_social);
    const socialEnd = formatDateTime(startDate, event.end_social);
    const eventStart = workshopStart || socialStart || startDate;
    const eventEnd = socialEnd || workshopEnd || '';

    // Prepare performers
    const performers = event.workshop_instructors
        ? [
            {
                '@type': 'PerformingGroup',
                name: event.workshop_instructors,
            },
        ]
        : [];

    // Location information
    let location = null;
    if (event.location_name?.[0]) {
        location = {
            '@type': 'Place',
            name: event.location_name[0],
        };

        if (event.location_address?.[0]) {
            location.address = {
                '@type': 'PostalAddress',
                streetAddress: event.location_address[0],
            };
        }
    }

    // Build structured data
    const data = {
        '@context': 'https://schema.org',
        '@type': 'Event',
        name: hasOfficialEvent && event?.name ? t(event.name) : event?.slug ? t(event.slug) : '',
        startDate: eventStart,
        eventAttendanceMode: 'https://schema.org/OfflineEventAttendanceMode',
        eventStatus: 'https://schema.org/EventScheduled',
    };

    // Add optional fields
    if (eventEnd) data.endDate = eventEnd;
    if (location) data.location = location;
    if (ogImage?.src) data.image = [ogImage.src];
    if (event.excerpt_t) data.description = event?.slug
        ? t(
            `${pagePrefix}.metadata.description.${event.slug.toLowerCase()}`,
            t(`${pagePrefix}.metadata.description.default`)
        )
        : t(`${pagePrefix}.metadata.description.default`);

    // Add pricing info
    if (event.cost) {
        data.offers = {
            '@type': 'Offer',
            url: pageUrl,
            price: formatPrice(event.cost),
            priceCurrency: 'EUR',
            availability: 'https://schema.org/InStock',
            validFrom: eventStart,
        };
    }

    // Add performers and keywords
    if (performers.length > 0) {
        data.performer = performers.length === 1 ? performers[0] : performers;
    }

    if (event.dances?.length > 0) {
        data.keywords = event.dances.join(', ');
    }

    return data;
}