---
import { getLangFromUrl, useTranslations } from '~/i18n/utils';

const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);

interface Props {
  targetDate: Date | string;
  text: string;
}

const { targetDate, text } = Astro.props;
---

<div class="w-full p-4 rounded-xl bg-gradient-to-br from-red-900 to-red-400 bg-opacity-80 backdrop-blur-lg">
  <h2 class="text-mdl font-bold text-white text-center mb-2">{text}</h2>
  <div id="countdown" class="grid grid-cols-4 gap-2 text-center">
    <div class="bg-white bg-opacity-20 rounded-lg p-2">
      <span id="days" class="text-2xl font-bold text-white">00</span>
      <p class="text-xs text-white">{t('days')}</p>
    </div>
    <div class="bg-white bg-opacity-20 rounded-lg p-2">
      <span id="hours" class="text-2xl font-bold text-white">00</span>
      <p class="text-xs text-white">{t('hours')}</p>
    </div>
    <div class="bg-white bg-opacity-20 rounded-lg p-2">
      <span id="minutes" class="text-2xl font-bold text-white">00</span>
      <p class="text-xs text-white">{t('minutes')}</p>
    </div>
    <div class="bg-white bg-opacity-20 rounded-lg p-2">
      <span id="seconds" class="text-2xl font-bold text-white">00</span>
      <p class="text-xs text-white">{t('seconds')}</p>
    </div>
  </div>
</div>

<script define:vars={{ targetDate }}>
  function updateCountdown() {
    const now = new Date();
    const target = new Date(targetDate);
    const diff = Math.floor((target - now) / 1000);

    if (diff <= 0) {
      document.getElementById('countdown').innerHTML =
        "<p class='text-xl font-bold text-white'>Countdown Over!</p>";
      return;
    }

    const days = Math.floor(diff / (24 * 60 * 60));
    const hours = Math.floor((diff % (24 * 60 * 60)) / (60 * 60));
    const minutes = Math.floor((diff % (60 * 60)) / 60);
    const seconds = diff % 60;

    document.getElementById('days').textContent = days.toString().padStart(2, '0');
    document.getElementById('hours').textContent = hours.toString().padStart(2, '0');
    document.getElementById('minutes').textContent = minutes.toString().padStart(2, '0');
    document.getElementById('seconds').textContent = seconds.toString().padStart(2, '0');
  }

  updateCountdown();
  setInterval(updateCountdown, 1000);
</script>
