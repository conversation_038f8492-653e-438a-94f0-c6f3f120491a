---
import Layout from '~/layouts/PageLayout.astro';
import { getLangFromUrl, useTranslations } from '~/i18n/utils';
import ContactOptions from '~/components/common/ContactOptions.astro';

const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);

const contactOptions = [
  {
    type: 'whatsapp',
    icon: 'tabler:brand-whatsapp',
    title: 'WhatsApp',
    description: t('contactPage.whatsapp.description'),
    cta: t('contactPage.whatsapp.cta'),
    href: `https://wa.me/${import.meta.env.WHATSAPP_SUPPORT}`,
    bgColor: 'bg-green-500',
    hoverColor: 'hover:bg-green-700',
  },
  {
    type: 'email',
    icon: 'tabler:mail',
    title: t('contactPage.email'),
    description: t('contactPage.email.description'),
    cta: t('contactPage.email.cta'),
    href: `mailto:${import.meta.env.MAIL_SUPPORT}`,
    bgColor: 'bg-indigo-500',
    hoverColor: 'hover:bg-indigo-700',
  },
  {
    type: 'phone',
    icon: 'tabler:phone',
    title: t('contactPage.phone'),
    description: t('contactPage.phone.description'),
    cta: t('contactPage.phone.cta'),
    href: `tel:${import.meta.env.PHONE_SUPPORT}`,
    bgColor: 'bg-blue-500',
    hoverColor: 'hover:bg-blue-700',
  },
];

const metadata = {
  title: t('contactPage.metadata.title'),
  description: t('contactPage.metadata.description'),
};
---

<Layout metadata={metadata} noDonate>
  <ContactOptions
    title={t('contactPage.title')}
    subtitle={t('contactPage.subtitle')}
    contactOptions={contactOptions}
    location={t('contactPage.location')}
  />
</Layout>
