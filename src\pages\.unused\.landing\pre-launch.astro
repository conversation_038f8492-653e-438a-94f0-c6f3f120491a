---
import Layout from '~/layouts/LandingLayout.astro';

import Hero2 from '~/components/widgets/Hero2.astro';
import CallToAction from '~/components/widgets/CallToAction.astro';

const metadata = {
  title: 'Pre-Launch Landing Page',
};
---

<Layout metadata={metadata}>
  <!-- Hero2 Widget ******************* -->

  <Hero2
    tagline="Pre-launch Demo"
    title="Pre-launch Landing Page: Build the Hype Before the Big Reveal!"
    subtitle="Craft a tantalizing Coming Soon or Pre-Launch Landing Page that leaves visitors eagerly awaiting your launch."
    callToAction={{ text: 'Call to Action', href: '#', icon: 'tabler:square-rounded-arrow-right' }}
    callToAction2={{ text: 'Learn more', href: '#' }}
    image={{
      src: 'https://images.unsplash.com/photo-1558803116-c1b4ac867b31?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2069&q=80',
      alt: 'Store with a Coming Soon sign. Pre-launch Landing Page',
    }}
  />

  <CallToAction
    title="Coming soon"
    subtitle="We are working on the content of these demo pages. You will see them very soon. Stay tuned Stay tuned!"
    callToAction={{
      text: 'Get template',
      href: 'https://github.com/onwidget/astrowind',
      icon: 'tabler:download',
    }}
  />
</Layout>
