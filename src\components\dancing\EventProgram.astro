---
import { getLangFromUrl, useTranslations } from '~/i18n/utils';

const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);

interface Props {
  timeStartWorkshop: string;
  timeEndWorkshop: string;
  timeStartSocial: string;
  timeEndSocial: string;
  socialDances: string[];
  workshop: string;
  instructors: string;
}

const { timeStartWorkshop, timeEndWorkshop, timeStartSocial, timeEndSocial, workshop, socialDances, instructors } =
  Astro.props;

const formatTime = (time: string) => (time ? `<span class="font-bold">${time}</span>` : '');
const formatWorkshop = () => (workshop ? `${workshop} ${t('workshop')}` : '');
const formatInstructors = () => (instructors ? `${t('with')} ${instructors}.` : '');
const formatSocialDances = () => (socialDances?.length ? `· ${socialDances.join(', ')}.` : '');
---

<pre
  style="white-space: pre-line;">
    <span class="uppercase">📢 {t('program')}</span>
    <span set:html={`${formatTime(timeStartWorkshop)} ~ ${formatTime(timeEndWorkshop)} ${formatWorkshop()} ${formatInstructors()}`} />
    <span set:html={`${formatTime(timeStartSocial)} ~ ${formatTime(timeEndSocial)} Social ${formatSocialDances()}`} />
</pre>
