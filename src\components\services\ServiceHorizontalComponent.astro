---
import { getLangFromUrl, useTranslations, useTranslatedPath } from '~/i18n/utils';
import { Image } from 'astro:assets'; // With this component, images are stored locally

const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);
const translatePath = useTranslatedPath(lang);

export interface Props {
  name: string;
  shortDescription: string;
  slug?: string;
  emoji?: string;
  cover?: any;
}

const { name, shortDescription, emoji, cover, slug } = Astro.props;
// console.log("cover:", cover)
const urlComputed = translatePath(`/services`) + `/${slug}` || '#';
---

<div class="max-w-6xl mx-auto p-4 sm:px-6 h-full">
  <!-- Blog post -->
  <article class="max-w-sm mx-auto md:max-w-none grid md:grid-cols-2 gap-6 md:gap-8 lg:gap-12 xl:gap-16 items-center">
    <a class="relative block group" href={urlComputed}>
      <div
        class="absolute inset-0 bg-gray-100 dark:bg-gray-800 hidden md:block transform md:translate-y-2 md:translate-x-4 xl:translate-y-4 xl:translate-x-8 group-hover:translate-x-0 group-hover:translate-y-0 transition duration-700 ease-out pointer-events-none"
        aria-hidden="true"
      >
      </div>
      <figure
        class="relative h-0 pb-[56.25%] md:pb-[75%] lg:pb-[56.25%] overflow-hidden transform md:-translate-y-2 xl:-translate-y-4 group-hover:translate-x-0 group-hover:translate-y-0 transition duration-700 ease-out"
      >
        <img
          src={cover.src}
          class="absolute inset-0 w-full h-full object-cover transform hover:scale-105 transition duration-700 ease-out"
        />

        <!-- <Image
          class="absolute inset-0 transform hover:scale-105 transition duration-700 ease-out"
          src={cover[0].url}
          loading="eager"
          width={400}
          height={400}
          decoding="async"
          alt=""
        /> -->
      </figure>
    </a>
    <div>
      <header>
        <!-- <div class="mb-3">
          <ul class="flex flex-wrap text-xs font-medium -m-1">
            <li class="m-1">
              <a
                class="inline-flex text-center text-gray-100 py-1 px-3 rounded-full bg-purple-600 hover:bg-purple-700 transition duration-150 ease-in-out"
                href={urlComputed}>Product</a
              >
            </li>
            <li class="m-1">
              <a
                class="inline-flex text-center text-gray-100 py-1 px-3 rounded-full bg-blue-500 hover:bg-blue-600 transition duration-150 ease-in-out"
                href={urlComputed}>Engineering</a
              >
            </li>
          </ul>
        </div> -->
        {
          name && (
            <h3 class="text-2xl lg:text-3xl font-bold leading-tight mb-2">
              <a class="hover:text-gray-100 transition duration-150 ease-in-out" href={urlComputed}>
                {name}
              </a>
            </h3>
          )
        }
      </header>
      {shortDescription && <p class="text-lg text-gray-400 flex-grow">{shortDescription}</p>}

      <div class="flex justify-between mt-3 item-center">
        <!-- <h1 class="text-lg font-bold text-gray-700 dark:text-gray-200 md:text-xl">$220</h1> -->
        <a
          href={urlComputed}
          class="px-2 py-1 text-xs font-bold text-white uppercase transition-colors duration-300 transform bg-gray-800 rounded dark:bg-gray-700 hover:bg-gray-700 dark:hover:bg-gray-600 focus:outline-none focus:bg-gray-700 dark:focus:bg-gray-600"
        >
          {t('readMore')}
        </a>
      </div>
    </div>
  </article>
</div>
