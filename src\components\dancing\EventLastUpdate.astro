---
import { Icon } from 'astro-icon/components';
import { getLangFromUrl, useTranslations } from '~/i18n/utils';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';

dayjs.extend(utc);
dayjs.extend(timezone);

const tz = 'Europe/Madrid';

interface Props {
  updatedAt: string;
}

const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);
const { updatedAt } = Astro.props;

const formattedDate = dayjs(updatedAt).tz(tz);
---

<div class="text-muted text-xs my-2">
  <Icon name="tabler:clock" width={20} height={20} class="inline-block mr-1" />
  {t('lastUpdate')}:
  <!-- <time class="inline-block" datetime={dayjs(updatedAt).tz(tz).format('ddd MMM DD YYYY HH:mm:ss [GMT]ZZ')}>
    {dayjs(updatedAt).tz(tz).format('DD/MM/YYYY')}
  </time> -->
  <time class="inline-block" datetime={formattedDate.format()}>
    {formattedDate.format('DD/MM/YYYY')}
  </time>
</div>
