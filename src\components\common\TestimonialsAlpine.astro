<section class="bg-white dark:bg-gray-900" x-data="{ testimonialActive: 1 }" x-cloak>
  <div class="container px-6 py-10 mx-auto">
    <div class="lg:-mx-6 lg:flex lg:items-center">
      <img
        class="object-cover object-center lg:w-1/2 lg:mx-6 w-full h-96 rounded-lg lg:h-[36rem]"
        src="https://images.unsplash.com/photo-1499470932971-a90681ce8530?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1470&q=80"
        alt=""
      />

      <div class="mt-8 lg:w-1/2 lg:px-6 lg:mt-0">
        <p class="text-5xl font-semibold text-blue-500">“</p>

        <h1 class="text-2xl font-semibold text-gray-800 dark:text-white lg:text-3xl lg:w-96">
          Help us improve our productivity
        </h1>

        <p class="max-w-lg mt-6 text-gray-500 dark:text-gray-400" x-text="'Testimonianza ' + testimonialActive"></p>

        <h3 class="mt-6 text-lg font-medium text-blue-500">Mia Brown</h3>
        <p class="text-gray-600 dark:text-gray-300">Marketing Manager at Stech</p>

        <div class="flex items-center justify-between mt-12 lg:justify-start">
          <button
            title="left arrow"
            x-on:click="testimonialActive = testimonialActive === 1 ? 3 : testimonialActive - 1"
            class="p-2 text-gray-800 transition-colors duration-300 border rounded-full rtl:-scale-x-100 dark:border-gray-700 dark:text-gray-200 dark:hover:bg-gray-800 hover:bg-gray-100"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="w-6 h-6"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
              stroke-width="2"
            >
              <path stroke-linecap="round" stroke-linejoin="round" d="M15 19l-7-7 7-7"></path>
            </svg>
          </button>

          <button
            title="right arrow"
            x-on:click="testimonialActive = testimonialActive >= 3 ? 1 : testimonialActive + 1"
            class="p-2 text-gray-800 transition-colors duration-300 border rounded-full rtl:-scale-x-100 dark:border-gray-700 dark:text-gray-200 dark:hover:bg-gray-800 lg:mx-6 hover:bg-gray-100"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="w-6 h-6"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
              stroke-width="2"
            >
              <path stroke-linecap="round" stroke-linejoin="round" d="M9 5l7 7-7 7"></path>
            </svg>
          </button>
        </div>
      </div>
    </div>
  </div>
</section>
