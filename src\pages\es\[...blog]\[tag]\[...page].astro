---
import type { InferGetStaticPropsType, GetStaticPaths } from 'astro';
import { blogTagRobots, getStaticPathsBlogTag, filterPostsByLocale } from '~/utils/blog';
import { getLangFromUrl } from '~/i18n/utils';

import Layout from '~/layouts/PageLayout.astro';
import BlogList from '~/components/blog/List.astro';
import Headline from '~/components/blog/Headline.astro';
import Pagination from '~/components/blog/Pagination.astro';

const lang = getLangFromUrl(Astro.url);

export const prerender = true;

export const getStaticPaths = (async ({ paginate }) => {
  return await getStaticPathsBlogTag({ paginate });
}) satisfies GetStaticPaths;

type Props = InferGetStaticPropsType<typeof getStaticPaths>;

const { page, tag } = Astro.props as Props;

const currentPage = page.currentPage ?? 1;

const posts = filterPostsByLocale(page.data, lang);

const metadata = {
  title: `Posts by tag '${tag}'${currentPage > 1 ? ` — Page ${currentPage} ` : ''}`,
  robots: {
    index: blogTagRobots?.index,
    follow: blogTagRobots?.follow,
  },
};
---

<Layout metadata={metadata}>
  <section class="px-4 md:px-6 py-12 sm:py-16 lg:py-20 mx-auto max-w-4xl">
    <Headline>Tag: {tag}</Headline>
    <!-- <BlogList posts={page.data} /> -->
    <BlogList posts={posts} />
    <Pagination prevUrl={page.url.prev} nextUrl={page.url.next} />
  </section>
</Layout>
