---
import Hero from '~/components/widgets/Hero.astro';
import Steps from '~/components/widgets/Steps.astro';
import Steps2 from '~/components/widgets/Steps2.astro';
import Layout from '~/layouts/PageLayout.astro';
import CallToAction from '~/components/widgets/CallToAction.astro';
import FollowSectionSocials from '~/components/common/FollowSectionSocials.astro';

import { getLangFromUrl, useTranslations, useTranslatedPath } from '~/i18n/utils';

const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);
const translatePath = useTranslatedPath(lang);

const metadata = {
  title: t('aboutPage.metadata.title'),
  description: t('aboutPage.metadata.description'),
};
---

<Layout metadata={metadata}>
  <Hero
    tagline={t('aboutPage.hero.tagline')}
    image={{
      src: '~/assets/images/heroes/about_us.webp',
      alt: 'working',
    }}
  >
    <Fragment slot="title">{t('aboutPage.hero.title')}</Fragment>

    <Fragment slot="subtitle">
      {t('aboutPage.hero.subtitle')}
    </Fragment>
  </Hero>

  <Steps
    title={t('aboutPage.steps.title')}
    items={t('aboutPage.steps.items')}
    image={{
      src: '~/assets/images/ctas/timeline.jpg',
      alt: t('aboutPage.steps.image.alt'),
    }}
  />

  <Steps2
    title={t('aboutPage.steps2.title')}
    subtitle={t('aboutPage.steps2.subtitle')}
    isReversed={true}
    callToAction={{
      text: t('aboutPage.steps2.ctaText'),
      href: '/',
    }}
    items={t('aboutPage.steps2.items')}
  />

  <FollowSectionSocials section="livegc" ctaText={t('followUs')} />

  <CallToAction
    title={t('aboutPage.cta.title')}
    subtitle={t('aboutPage.cta.subtitle')}
    callToAction={{
      text: t('aboutPage.cta.ctaText'),
      href: translatePath('contact'),
    }}
  />
</Layout>
