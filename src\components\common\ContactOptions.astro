---
import { Icon } from 'astro-icon/components';

export interface ContactOption {
  type: string;
  icon: string;
  title: string;
  description: string;
  cta: string;
  href: string;
  bgColor: string;
  hoverColor: string;
}

interface Props {
  title: string;
  subtitle: string;
  contactOptions: ContactOption[];
  location: string;
}

const { title, subtitle, contactOptions, location } = Astro.props;
---

<div class="min-h-screen bg-white dark:bg-gray-900 flex items-center justify-center p-4 sm:px-6 lg:px-8">
  <div class="max-w-5xl w-full space-y-8">
    <div class="text-center">
      <h2 class="mt-6 text-3xl font-extrabold text-gray-900 dark:text-white sm:text-4xl">
        {title}
      </h2>
      <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">
        {subtitle}
      </p>
    </div>

    <div class="mt-8 space-y-6">
      <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 md:grid-cols-3">
        {contactOptions.map(({ type, icon, title, description, cta, href, bgColor, hoverColor }) => (
          <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg transition-transform duration-300 hover:scale-105">
            <div class="px-6 py-5 sm:p-6">
              <div class="flex items-center">
                <div class={`flex-shrink-0 ${bgColor} rounded-md p-3`}>
                  <Icon name={icon} class="h-6 w-6 text-white" />
                </div>
                <div class="ml-4">
                  <h3 class="text-lg font-medium leading-6 text-gray-900 dark:text-white">{title}</h3>
                  <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
                    {description}
                  </p>
                </div>
              </div>
              <div class="mt-5">
                <a
                  href={href}
                  target={type === 'whatsapp' ? '_blank' : '_self'}
                  rel={type === 'whatsapp' ? 'noopener noreferrer' : ''}
                  class={`inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white ${bgColor} ${hoverColor} focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-${bgColor.split('-')[1]}-500 transition-colors duration-300`}
                >
                  {cta}
                </a>
              </div>
            </div>
          </div>
        ))}
      </div>

      <div class="mx-auto text-center">
        <Icon name="tabler:map-pin" width={20} height={20} class="inline-block -mt-0.5" />
        <small class="mx-2 text-gray-700 dark:text-gray-400">{location}</small>
      </div>
    </div>
  </div>
</div>