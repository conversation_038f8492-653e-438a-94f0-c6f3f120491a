---
import '~/assets/styles/tailwind.css';

import { I18N } from '~/utils/config';

import CommonMeta from '~/components/common/CommonMeta.astro';
import Favicons from '~/components/Favicons.astro';
import CustomStyles from '~/components/CustomStyles.astro';
import ApplyColorMode from '~/components/common/ApplyColorMode.astro';
import Metadata from '~/components/common/Metadata.astro';
import BasicScripts from '~/components/common/BasicScripts.astro';
// import SiteVerification  from "~/components/common/SiteVerification.astro"
// import Analytics  from "~/components/common/Analytics.astro"

import type { MetaData as MetaDataType } from '~/types';
import CookieConsent from '~/components/common/CookieConsent.astro';
import NoCache from '~/components/common/NoCache.astro';

import { getLangFromUrl } from '../i18n/utils';
// import RemoveSlash from '~/components/common/RemoveSlash.astro';

const lang = getLangFromUrl(Astro.url);

export interface Props {
  metadata?: MetaDataType;
}

const { metadata = {} } = Astro.props;
const { language, textDirection } = I18N;
---

<!doctype html>
<!-- <html lang={language} dir={textDirection} class="2xl:text-[20px]"> -->
<html lang={lang} dir={textDirection} class="2xl:text-[20px]">
  <head>
    <NoCache />
    <CommonMeta />
    <Favicons />
    <CustomStyles />
    <ApplyColorMode />
    <Metadata {...metadata} />
    <CookieConsent />
    <!-- <RemoveSlash/> -->
    <!-- <SiteVerification /> -->
    <!-- <Analytics /> -->
  </head>

  <body class="antialiased text-default bg-page tracking-tight">
    <slot />

    <BasicScripts />

    <style is:global>
      img {
        content-visibility: auto;
      }
    </style>
  </body>
</html>
