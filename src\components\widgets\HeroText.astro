---
import { Icon } from 'astro-icon/components';

const {
  title = await Astro.slots.render('title'),
  subtitle = await Astro.slots.render('subtitle'),
  tagline,
  content = await Astro.slots.render('content'),
  callToAction = await Astro.slots.render('callToAction'),
  callToAction2 = await Astro.slots.render('callToAction2'),
} = Astro.props;
---

<section class="relative md:-mt-[76px] not-prose">
  <div class="absolute inset-0 pointer-events-none" aria-hidden="true"></div>
  <div class="relative max-w-7xl mx-auto px-4 sm:px-6">
    <div class="pt-0 md:pt-[76px] pointer-events-none"></div>
    <div class="py-12 md:py-20 pb-8 md:pb-8">
      <div class="text-center max-w-5xl mx-auto">
        {tagline && (
          <p
            class="text-base text-secondary dark:text-blue-200 font-bold tracking-wide uppercase"
            set:html={tagline}
          />
        )}
        {
          title && (
            <h1
              class="text-5xl md:text-6xl font-bold leading-tighter tracking-tighter mb-4 font-heading dark:text-gray-200"
              set:html={title}
            />
          )
        }
        <div class="max-w-3xl mx-auto">
          {subtitle && <p class="text-xl text-muted mb-6 dark:text-slate-300" set:html={subtitle} />}
          <div class="max-w-xs sm:max-w-md m-auto flex flex-nowrap flex-col sm:flex-row sm:justify-center gap-4">
            {
              callToAction && (
                <div class="flex w-full sm:w-auto">
                  {typeof callToAction === 'string' ? (
                    <Fragment set:html={callToAction} />
                  ) : (
                    <a class="btn btn-primary sm:mb-0 w-full" href={callToAction?.href} target="_blank" rel="noopener">
                      {callToAction?.icon && (
                        <>
                          <Icon name={callToAction.icon} class="w-5 h-5 mr-1 -ml-1.5 rtl:-mr-1.5 rtl:ml-1" />
                        </>
                      )}
                      {callToAction?.text}
                    </a>
                  )}
                </div>
              )
            }
            {
              callToAction2 && (
                <div class="flex w-full sm:w-auto">
                  {typeof callToAction2 === 'string' ? (
                    <Fragment set:html={callToAction2} />
                  ) : (
                    <a class="btn w-full" href={callToAction2?.href}>
                      {callToAction2?.icon && (
                        <>
                          <Icon name={callToAction2.icon} class="w-5 h-5 mr-1 -ml-1.5" />
                          &nbsp;
                        </>
                      )}
                      {callToAction2.text}
                    </a>
                  )}
                </div>
              )
            }
          </div>
        </div>
        {content && <Fragment set:html={content} />}
      </div>
    </div>
  </div>
</section>
