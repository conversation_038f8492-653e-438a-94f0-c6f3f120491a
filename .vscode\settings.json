{"editor.acceptSuggestionOnEnter": "on", "frontMatter.taxonomy.contentTypes": [{"name": "default", "pageBundle": false, "fields": [{"title": "title", "name": "title", "type": "string"}, {"title": "date", "name": "date", "type": "datetime"}, {"title": "image", "name": "image", "type": "image"}, {"title": "excerpt", "name": "excerpt", "type": "string"}, {"title": "category", "name": "category", "type": "categories"}, {"title": "tags", "name": "tags", "type": "tags"}]}]}