---
import { SITE } from '~/utils/config';
import logo from '~/assets/images/ui/logo.svg';
import { Image } from 'astro:assets'; // With this component, images are stored locally
---

<span
  class="flex self-center rtl:ml-0 rtl:mr-2 text-2xl md:text-xl font-bold text-gray-900 whitespace-nowrap dark:text-white"
>
  <Image
    src={logo}
    alt={SITE?.name + '- Logo'}
    class="w-8 h-8 rounded-full ring-2 ring-white dark:ring-white mr-4"
    loading="eager"
    width={32}
    height={32}
    loading="eager"
    decoding="async"
  />
  <span> {SITE?.name}</span>
</span>
