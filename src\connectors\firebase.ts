// import { initializeApp } from "firebase/app";
// import { getFirestore, collection, query, where, getDocs } from "firebase/firestore";

// const firebaseConfig = {
//     apiKey: import.meta.env.API_KEY,
//     authDomain: import.meta.env.AUTH_DOMAIN,
//     projectId: import.meta.env.PROJECT_ID,
//     storageBucket: import.meta.env.STORAGE_BUCKET,
//     messagingSenderId: import.meta.env.MESSAGING_SENDER_ID,
//     appId: import.meta.env.APP_ID,
// };

// const app = initializeApp(firebaseConfig);
// const db = getFirestore(app);

// export const getEvents = async () => {

//     try {
//         const q = query(collection(db, "events"), where("approvedAt", "!=", null));

//         const events = [];
//         const querySnapshot = await getDocs(q);

//         querySnapshot.forEach((doc) => {

//             const tmp = doc.data();
//             tmp.id = doc.id
//             events.push(tmp);

//             // console.log(doc.id, " => ", doc.data());
//         });

//         return events

//     } catch (error) { console.error('Error:', error); }
// };



