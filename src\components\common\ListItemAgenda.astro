---
import { getLangFromUrl, useTranslations, useTranslatedPath } from '~/i18n/utils';
import type { EventInfo } from '~/types';
import { Image } from 'astro:assets';
import card_placeholder from '~/assets/images/common/400x400.svg';

import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import es from 'dayjs/locale/es';

import slugify from 'limax';

dayjs.extend(utc).extend(timezone);
const TZ = 'Europe/Madrid';

export interface Props {
  event: EventInfo;
  flyersPreview?: boolean;
  pathPrefix: string;
  linkAsSlideshow?: boolean;
}

const { event, pathPrefix = '', linkAsSlideshow = false } = Astro.props;

const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);
const translatePath = useTranslatedPath(lang);

const hasFlyerEn = !!event?.flyer;
const hasFlyerEs = !!event?.flyer_es;
const hasOfficialEvent = hasFlyerEn || hasFlyerEs;

const eventDate = dayjs(event?.start).tz(TZ);
const eventDay = eventDate.format('D');
const eventMonth = eventDate.locale(lang === 'es' ? es : lang).format('MMM');
const dayOfWeek = eventDate.day() || 7; // Convert 0 (Sunday) to 7

const eventCount = event?.other_events?.length ?? 0;
const hasUnofficialEvents = eventCount > 0;

const getEventText = (hasOfficialEvent: boolean, eventCount: number) => {
  if (hasOfficialEvent) return t('officialEvent');
  if (eventCount === 0) return t('noEvents');
  const key = eventCount > 1 ? 'events' : 'event';
  return `${eventCount} ${t(key)}`;
};

const eventText = getEventText(hasOfficialEvent, eventCount);
const eventName = hasOfficialEvent ? event?.name : event.slug;
const path = `${pathPrefix}/${slugify(t(event.slug))}`;

const getFlyer = (hasOfficialEvent: boolean, lang: string, hasFlyerEs: boolean) => {
  if (!hasOfficialEvent) return card_placeholder;

  // Caso 1: lingua spagnola e flyer_es disponibile
  if (lang === 'es' && hasFlyerEs && event?.flyer_es?.[0]?.url) {
    return event.flyer_es[0].url;
  }

  // Caso 2: lingua spagnola, no flyer_es, ma flyer disponibile
  if (lang === 'es' && event?.flyer?.[0]?.url) {
    return event.flyer[0].url;
  }

  // Caso 3: lingua non spagnola, no flyer ma flyer_es disponibile
  if (lang !== 'es' && !event?.flyer?.[0]?.url && event?.flyer_es?.[0]?.url) {
    return event.flyer_es[0].url;
  }

  // Caso 4: flyer principale disponibile
  if (event?.flyer?.[0]?.url) {
    return event.flyer[0].url;
  }

  // Default: nessun flyer disponibile
  return card_placeholder;
};

const flyer = getFlyer(hasOfficialEvent, lang, hasFlyerEs);

const linkComputed =
  linkAsSlideshow && hasOfficialEvent ? `${translatePath(path)}#${t('flyer').toLowerCase()}-0` : translatePath(path);

const getImageClass = (hasOfficialEvent: boolean, hasUnofficialEvents: boolean) => {
  let className = 'w-full h-full object-cover';
  if (!hasOfficialEvent) className += ' rounded-full';
  if (hasOfficialEvent || hasUnofficialEvents) className += ' border-selected';
  if (!hasOfficialEvent && !hasUnofficialEvents) className += ' grayscale';
  return className;
};

const imageClass = getImageClass(hasOfficialEvent, hasUnofficialEvents);

const getEventTextClass = (hasOfficialEvent: boolean) =>
  `text-sm truncate ${hasOfficialEvent ? 'text-orange-500 dark:text-orange-400' : 'text-gray-500 dark:text-gray-400'}`;

const eventTextClass = getEventTextClass(hasOfficialEvent);
---

<li
  class="list-item hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200"
  data-day-of-week={dayOfWeek}
>
  <a href={linkComputed} aria-label={eventName} class="flex items-center space-x-4 rtl:space-x-reverse p-4">
    <div class="flex-shrink-0 w-12 h-12">
      <Image
        src={flyer}
        alt={t(event?.excerpt_t) || ''}
        loading="eager"
        width={400}
        height={400}
        decoding="async"
        class={imageClass}
      />
    </div>
    <div class="flex-1 min-w-0">
      <p class="text-sm font-medium text-gray-900 truncate dark:text-white">{t(eventName)}</p>
      <p class={eventTextClass}>
        {eventText}
      </p>
    </div>
    <div class="grid ml-auto place-items-center justify-self-end">
      <div class="px-2 py-1 text-xs uppercase select-none whitespace-nowrap text-center">
        <span class="block">{eventDay}</span>
        <span class="block">{eventMonth}</span>
      </div>
    </div>
  </a>
</li>

<script>
  function highlightCurrentDay() {
    const today = new Date();
    const currentDayOfWeek = today.getDay() || 7; // Convert 0 (Sunday) to 7
    const listItems = document.querySelectorAll('.list-item');

    listItems.forEach((item) => {
      const itemDayOfWeek = parseInt(item.dataset.dayOfWeek, 10);
      if (itemDayOfWeek === currentDayOfWeek) {
        item.classList.add('current-day');
      }
    });
  }

  // Esegui la funzione immediatamente e di nuovo dopo il caricamento del DOM
  highlightCurrentDay();
  document.addEventListener('DOMContentLoaded', highlightCurrentDay);
</script>

<style>
  .border-selected {
    @apply p-0.5;
    background: conic-gradient(from 180deg at 50% 50%, #f09433, #e6683c, #dc2743, #cc2366, #bc1888, #e6683c, #f09433);
  }

  .list-item {
    position: relative;
    overflow: hidden;
  }

  .current-day::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background-color: orange;
    z-index: 10;
  }

  .current-day {
    background-color: rgba(128, 128, 128, 0.1);
  }
</style>
