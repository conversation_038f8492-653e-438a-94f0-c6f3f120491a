---
import Layout from '~/layouts/PageLayout.astro';
import PropertyDetail from '~/components/real-estate/PropertyDetail.astro';
import { getImage } from 'astro:assets';
import { findPropertyByName } from '~/utils/property-page-utils';

export interface Props {
  propertyName: string;
  lang: string;
}

const { propertyName, lang } = Astro.props;

// Utilizza la funzione comune per trovare la proprietà per Name
const propertyData = findPropertyByName(propertyName);

if (!propertyData) {
  Astro.response.status = 404;
}

// Seleziona la descrizione e il titolo in base alla lingua
const seoDescription = lang === 'en' ? propertyData?.seo_description_en : propertyData?.seo_description_es;
const title = lang === 'en' ? propertyData?.title_en : propertyData?.title_es;

// Preparazione immagine per OpenGraph
let ogImage: any;
try {
  // Utilizziamo og_image se disponibile, altrimenti fallback su cover o images
  const ogImageSource = propertyData?.og_image?.[0] || propertyData?.cover?.[0] || propertyData?.images?.[0];

  if (ogImageSource) {
    ogImage = await getImage({
      src: ogImageSource.url,
      width: 1200,
      height: 630,
      format: 'webp',
    });
  }
} catch (error) {
  console.error("Errore nel processare l'immagine:", error);
}

// Metadata per la pagina
const metadata = {
  title,
  description: seoDescription,
  openGraph: {
    images: ogImage
      ? [
          {
            url: ogImage.src,
            width: ogImage.attributes.width,
            height: ogImage.attributes.height,
          },
        ]
      : [],
  },
};
---

<Layout metadata={metadata} noDonate>
  {propertyData ? <PropertyDetail {...propertyData} /> : <div>Property not found</div>}
</Layout>
