---
import PostTags from '~/components/blog/Tags.astro';
interface Props {
  name: string;
  excerpt?: string;
  dances?: any[];
  flyerUrl?: string;
}

// const hasDances = dances?.length;

const { name, excerpt, dances, flyerUrl } = Astro.props;
---

<!-- Event Name -->{
  name && (
    <h1 class="text-center px-4 sm:px-6 max-w-3xl mx-auto text-4xl md:text-5xl font-bold leading-tighter tracking-tighter font-heading">
      {name}
    </h1>
  )
}

<!-- Event Excerpt -->
<div class="flex justify-center max-w-3xl mx-auto mt-4 mb-8 px-4 sm:px-6">
  {excerpt && <p class="text-xl md:text-2xl text-muted dark:text-slate-400 mr-4">{excerpt || ''} </p>}
  {dances?.length && <PostTags tags={dances} class="mr-5 rtl:mr-0 rtl:ml-5" />}
</div>

<!-- Flyer -->
{
  flyerUrl ? (
    <img
      src={flyerUrl?.src}
      class="px-4 sm:px-6 max-w-full lg:max-w-[900px] mx-auto mb-6 sm:rounded-md"
      alt={excerpt || ''}
      width={flyerUrl?.width || 1080}
      height={flyerUrl?.height || 1080}
      loading="eager"
      decoding="async"
    />
  ) : (
    <div class="max-w-3xl mx-auto px-4 sm:px-6">
      <div class="border-t dark:border-slate-700" />
    </div>
  )
}