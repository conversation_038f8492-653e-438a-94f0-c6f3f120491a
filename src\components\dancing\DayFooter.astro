---
import SocialShare from '~/components/common/SocialShare.astro';
import type { EventInfo } from '~/types';
import { getLangFromUrl, useTranslations } from '~/i18n/utils';

import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';

dayjs.extend(utc);
dayjs.extend(timezone);

const tz = "Europe/Madrid"

interface Props {
  event?: EventInfo;
  pageTitle: string;
  fullUrl: string;
}

const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);

const { pageTitle, event, fullUrl } = Astro.props;
---

<div class="flex justify-between my-4">
  <!-- https://add-to-calendar-button.com/use-with-astro -->
  {
    event ? (
      <add-to-calendar-button
        name={`${pageTitle} (${t(event?.excerpt_t)})`}
        options="'Apple','Google'"
        location={`${event?.location_name} - ${event?.location_address}`}
        description={fullUrl}
        startDate={dayjs(event?.start).tz(tz).format('YYYY-MM-DD')}
        endDate={dayjs(event?.end).tz(tz).format('YYYY-MM-DD')}
        startTime={dayjs(event?.start).tz(tz).format('HH:mm')}
        endTime={dayjs(event?.end).tz(tz).format('HH:mm')}
        timeZone="Atlantic/Canary"
        options="'Google', 'Apple'"
        hideCheckmark
        size="2"
        buttonStyle="3d"
        lightMode="bodyScheme"
        label={t('addToCalendar')}
      />
    ) : (
      <span />
    )
  }

  <SocialShare url={fullUrl} text={pageTitle} class="mt-2 sm:mt-1 align-middle text-gray-500 dark:text-slate-600" />
</div>
