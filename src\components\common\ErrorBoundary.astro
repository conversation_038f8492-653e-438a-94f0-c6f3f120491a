---
interface Props {
  fallback?: string;
}

const { fallback = 'Something went wrong. Please try again later.' } = Astro.props;

// In Astro, we can use try-catch in the component script
let hasError = false;
let errorMessage = '';

try {
  await Astro.slots.render('default');
} catch (error) {
  hasError = true;
  errorMessage = error instanceof Error ? error.message : String(error);
  console.error('Error in ErrorBoundary:', error);
}
---

{
  hasError ? (
    <div class="error-boundary bg-red-50 border border-red-200 rounded-lg p-4 my-4">
      <p class="text-red-700 text-sm font-medium">{fallback}</p>
      {import.meta.env.DEV && (
        <details class="mt-2">
          <summary class="text-red-600 text-xs cursor-pointer">Technical Details</summary>
          <pre class="mt-2 text-xs text-red-600 whitespace-pre-wrap">{errorMessage}</pre>
        </details>
      )}
    </div>
  ) : (
    <slot />
  )
}

<style>
  .error-boundary {
    --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1);
    box-shadow: var(--tw-shadow);
  }
</style>
