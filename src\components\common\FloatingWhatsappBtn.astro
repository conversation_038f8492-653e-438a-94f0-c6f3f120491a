---
// https://tailwindcomponents.com/component/floating-button
import { Icon } from 'astro-icon/components';

interface Props {
  number: string;
}

const { number } = Astro.props;
---

<div class="group fixed bottom-0 right-0 p-2 flex items-end justify-end w-24 h-24" style="z-index: 999;">
  <a
    href={`https://wa.me/${number}`}
    target="_blank"
    class="no-underline cursor-pointer text-white shadow-xl flex items-center justify-center p-3 rounded-full bg-green-600 hover:bg-green-500 z-100 absolute hover:scale-110 transition-transform duration-300"
    style="color: white;"
  >
    <Icon name="tabler:brand-whatsapp" width={36} height={36} class="inline-block" />
  </a>
</div>
