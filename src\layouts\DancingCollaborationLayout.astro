---
import Layout from '~/layouts/PageLayout.astro';
import CallToAction from '~/components/widgets/CallToAction.astro';
import Stats from '~/components/widgets/Stats.astro';
import Hero2 from '~/components/widgets/Hero2.astro';
import Features2 from '~/components/widgets/Features2.astro';
import DancingTestimonials from '~/components/dancing/DancingTestimonials.astro';
import Content from '~/components/widgets/Content.astro';
import DancePastEventsCta from '~/components/dancing/DancePastEventsCta.astro';
import Steps from '~/components/widgets/Steps.astro';
import FollowSectionSocials from '~/components/common/FollowSectionSocials.astro';

import { getLangFromUrl, useTranslations, useTranslatedPath } from '~/i18n/utils';
import Partnerships from '~/components/Partnerships.astro';
import FloatingWhatsappBtn from '~/components/common/FloatingWhatsappBtn.astro';

const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);
const translatePath = useTranslatedPath(lang);

const metadata = {
  title: t('dancingCollaborations.meta.title'),
  description: t('dancingCollaborations.meta.description'),
};
---

<Layout metadata={metadata}>
  <Hero2
    tagline={t('dancingCollaborations.hero2.tagline')}
    callToAction={{
      text: t('contact'),
      href: translatePath('/contact'),
      icon: 'tabler:message',
    }}
    callToAction2={{ text: t('readMore'), href: '#thanks' }}
  >
    <Fragment slot="title">{t('dancingCollaborations.hero2.title')}</Fragment>

    <Fragment slot="subtitle">
      <span class="hidden sm:inline">
        {t('dancingCollaborations.hero2.subtitle')}
      </span>
    </Fragment>

    <Fragment slot="image">
      <div class="relative h-0 pb-[56.25%]">
        <iframe
          width="560"
          height="315"
          src="https://www.youtube.com/embed/cmR3DMBIhEU"
          title={t('dancingCollaborations.hero2.video.title')}
          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
          allowfullscreen
          class="absolute top-0 left-0 w-full h-full"
        >
        </iframe>
      </div>
    </Fragment>
  </Hero2>

  <div
    id="thanks"
    class="w-full dark:bg-gray-800 mb-16"
    style="background-image: url('/src/assets/images/heroes/something_great.jpg.webp'); background-position: center center; background-blend-mode: multiply; background-size: cover;"
  >
    <div class="container flex flex-col flex-wrap content-center justify-center p-4 py-20 mx-auto md:p-10">
      <p class="text-center text-base text-secondary dark:text-blue-200 font-bold tracking-wide uppercase">
        {t('dancingCollaborations.thanksBanner.tagline')}
      </p>

      <h1 class="text-5xl antialiased font-semibold leadi text-center dark:text-gray-100">
        {t('dancingCollaborations.thanksBanner.title')}
      </h1>
      <p class="pt-2 pb-4 text-xl antialiased text-center dark:text-gray-100">
        {t('dancingCollaborations.thanksBanner.text')}
      </p>
    </div>
  </div>

  <Content
    isReversed
    isAfterContent={true}
    items={t('dancingCollaborations.content1.items')}
    image={{
      alt: t('dancingCollaborations.content1.image.alt'),
      src: '~/assets/images/contents/target.webp',
    }}
  >
    <Fragment slot="content">
      <h3 class="text-2xl font-bold tracking-tight dark:text-white sm:text-3xl mb-2">
        {t('dancingCollaborations.content1.title')}
      </h3>
    </Fragment>

    <Fragment slot="bg">
      <div class="absolute inset-0 bg-blue-50 dark:bg-transparent"></div>
    </Fragment>
  </Content>

  <Features2
    columns={2}
    title={t('dancingCollaborations.features2.title')}
    subtitle={t('dancingCollaborations.features2.subtitle')}
    items={t('dancingCollaborations.features2.items')}
  >
    <Fragment slot="bg">
      <div class="absolute inset-0 bg-blue-50 dark:bg-transparent"></div>
    </Fragment>
  </Features2>

  <CallToAction
    tagline={t('dancingCollaborations.cta1.tagline')}
    title={t('dancingCollaborations.cta1.title')}
    subtitle={t('dancingCollaborations.cta1.subtitle')}
    callToAction={{
      text: t('contact'),
      href: translatePath('/contact'),
    }}
  />

  <Content
    isAfterContent={true}
    items={t('dancingCollaborations.content2.items')}
    image={{
      src: '~/assets/images/contents/formula.webp',
      alt: t('dancingCollaborations.content2.image.alt'),
    }}
  >
    <Fragment slot="content">
      <h3
        class="text-2xl font-bold tracking-tight dark:text-white sm:text-3xl mb-2"
        set:html={t('dancingCollaborations.content2.title')}
      />
    </Fragment>

    <Fragment slot="bg">
      <div class="absolute inset-0 bg-blue-50 dark:bg-transparent"></div>
    </Fragment>
  </Content>

  <CallToAction
    tagline={t('dancingCollaborations.cta2.tagline')}
    title={t('dancingCollaborations.cta2.title')}
    subtitle={t('dancingCollaborations.cta2.subtitle')}
    callToAction={{
      text: t('contact'),
      href: translatePath('/contact'),
    }}
  />

  <Steps
    id="past_events_flyers_cta"
    isReversed
    title={t('dancingCollaborations.steps.title')}
    items={t('dancingCollaborations.steps.items')}
    image={{
      src: '~/assets/images/ctas/insta_flyers.webp',
      alt: t('dancingCollaborations.steps.image.alt'),
    }}
  />

  <DancePastEventsCta />

  <CallToAction
    tagline={t('dancingCollaborations.cta3.tagline')}
    title={t('dancingCollaborations.cta3.title')}
    subtitle={t('dancingCollaborations.cta3.subtitle')}
    callToAction={{
      text: t('contact'),
      href: translatePath('/contact'),
    }}
  />

  <Stats title={t('dancing.stats.title')} stats={t('dancing.stats.stats')} />

  <Partnerships title={t('partnerships.title')} disclaimer={t('partnerships.disclaimer')} />

  <DancingTestimonials />

  <FollowSectionSocials section="dancing" ctaText={t('followUs')} />

  <CallToAction
    tagline={t('dancingCollaborations.cta4.tagline')}
    title={t('dancingCollaborations.cta4.title')}
    subtitle={t('dancingCollaborations.cta4.subtitle')}
    callToAction={{
      text: t('contact'),
      href: translatePath('/contact'),
    }}
  />

  <FloatingWhatsappBtn number={import.meta.env.WHATSAPP_SUPPORT} />
</Layout>
