import Jimp from "jimp";
// import { generate } from 'text-to-image';

// export const createTextualImage = async (text) => {
//     try {
//         return await generate(text, {
//             debug: true,
//             debugFilename: 'debug_file.png',
//             bgColor: '#030620',
//             customHeight: 1080,
//             textAlign: 'center',
//             verticalAlign: 'center',
//             textColor: '#ffffff',
//         });
//     } catch (error) {
//         console.log('error:', error);
//     }
// }

// export const createSocialImageWithTextAndWatermark = async (text, watermarkUrl = 'src/assets/images/watermark.png') => {
//     const width = 1200;
//     const height = 630;
//     const backgroundColor = '#030620';
//     const image = new Jimp(width, height, backgroundColor);
//     const font = await Jimp.loadFont(Jimp.FONT_SANS_128_WHITE);

//     const textWidth = Jimp.measureText(font, text);
//     const textHeight = Jimp.measureTextHeight(font, text, width);

//     const x = (width - textWidth) / 2;
//     const y = (height - textHeight) / 2;

//     image.print(font, x, y, text);

//     // Carica l'immagine del watermark
//     const watermark = await Jimp.read(watermarkUrl);

//     // Ridimensiona il watermark alla dimensione desiderata
//     watermark.resize(1200, Jimp.AUTO);

//     // Posiziona il watermark nell'angolo in basso a destra
//     const watermarkX = width - watermark.bitmap.width - 10;
//     const watermarkY = height - watermark.bitmap.height - 10;

//     // Aggiungi il watermark all'immagine
//     image.composite(watermark, watermarkX, watermarkY);

//     // const percorsoFile = 'test.png';
//     // image.write(percorsoFile, (err) => {
//     //     if (err) throw err;
//     //     console.log('Immagine salvata con successo!');
//     // });

//     return await image.getBufferAsync(Jimp.MIME_PNG);
// }

export const createSocialImageWithText = async (text, backgroundUrl = 'src/assets/images/livegrancanaria_og_background.png') => {
    const width = 1200;
    const height = 630;
    const backgroundImage = await Jimp.read(backgroundUrl);
    backgroundImage.resize(width, height);

    const font = await Jimp.loadFont(Jimp.FONT_SANS_128_WHITE);
    const textWidth = Jimp.measureText(font, text);
    const textHeight = Jimp.measureTextHeight(font, text, width);
    const x = (width - textWidth) / 2;
    const y = (height - textHeight) / 2;
    backgroundImage.print(font, x, y, text);

    return await backgroundImage.getBufferAsync(Jimp.MIME_PNG);
}

export const createCollage = async (
    imageUrls,
    outputPath,
    margin,
    backgroundColor,
    height,
    watermarkUrl
) => {
    try {
        const images = await Promise.all(imageUrls.map((url) => Jimp.read(url)));

        if (!images?.length) return;

        // Ridimensiona tutte le immagini alla stessa altezza
        for (let img of images) {
            img.resize(Jimp.AUTO, height);
        }

        const collageWidth = images.reduce((sum, img) => sum + img.bitmap.width, 0) + margin * (images.length - 1);
        const collageHeight = height;

        // Crea un'immagine di sfondo con il colore specificato
        let background = new Jimp(collageWidth, collageHeight, backgroundColor);

        let xOffset = 0;
        for (let img of images) {
            background.composite(img, xOffset, 0);
            xOffset += img.bitmap.width + margin;
        }

        // Carica l'immagine del watermark
        let watermark = await Jimp.read(watermarkUrl);

        // Ridimensiona il watermark alla dimensione desiderata    
        watermark.resize(500, Jimp.AUTO);

        // Posiziona il watermark al centro dell'immagine
        const x = (background.bitmap.width - watermark.bitmap.width) / 2;
        const y = (background.bitmap.height - watermark.bitmap.height) / 2;

        // Sovrapponi il watermark all'immagine
        background.composite(watermark, x, y);

        // Ridimensiona l'immagine finale
        // background.resize(1200, 630);
        background.resize(1200, Jimp.AUTO);

        await background.writeAsync(outputPath);
        // console.log(`Collage saved as ${outputPath}`);

        // return await background.getBufferAsync(Jimp.MIME_PNG);
        // return background

    } catch (error) {
        console.error('There was an error making the collage:', error);
    }
};
