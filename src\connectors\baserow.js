import { getImage } from 'astro:assets';
import { uploadImageAndGetUrl } from 'src/connectors/pulpo';
import { createSocialImageWithText } from 'src/utils/imagesUtils';

const BASEROW_DB_TOKEN = import.meta.env.BASEROW_DB_TOKEN;
const BASE_API_URL = 'https://api.baserow.io/api';

const TABLE_IDS = {
    social_agenda: "392566",
    dancing_agenda: "392565",
    reviews: "392568",
    vocabulary: "392563",
    bio: "392562",
    services: "392564",
    housing: "392567",
    settings: "392575"
};

const TABLE_NAMES = Object.keys(TABLE_IDS);

async function fetchFromBaserow(tableId) {
    const response = await fetch(`${BASE_API_URL}/database/rows/table/${tableId}/?user_field_names=true`, {
        headers: {
            'Authorization': `Token ${BASEROW_DB_TOKEN}`,
            'Content-Type': 'application/json'
        }
    });

    if (!response.ok) {
        throw new Error(`Failed to fetch from Baserow: ${response.statusText}`);
    }

    const data = await response.json();
    return data.results;
}

async function updateBaserowRow(tableId, rowId, fields) {
    const response = await fetch(`${BASE_API_URL}/database/rows/table/${tableId}/${rowId}/?user_field_names=true`, {
        method: 'PATCH',
        headers: {
            'Authorization': `Token ${BASEROW_DB_TOKEN}`,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(fields)
    });

    if (!response.ok) {
        throw new Error(`Failed to update Baserow row: ${response.statusText}`);
    }

    return await response.json();
}

function mergeEvents(record) {
    const otherEvents = record.other_events || [];
    const fixedEvents = record.fixed_events || [];
    return [...otherEvents, ...fixedEvents];
}

async function createOgImage(record) {
    const mergedEvents = mergeEvents(record);
    const eventCount = mergedEvents.length;
    const textComputed = eventCount > 1 ? `${eventCount} eventos` : eventCount === 1 ? "1 evento" : "No hay eventos";

    const imageBuffer = await createSocialImageWithText(textComputed);
    const imageUrl = await uploadImageAndGetUrl(imageBuffer);

    return {
        id: record.id,
        og_image: [{ url: imageUrl, filename: `event-${record.id}.png` }]
    };
}

async function updateOgImages(tableName) {
    const tableId = TABLE_IDS[tableName];
    if (!tableId) throw new Error(`Invalid table name: ${tableName}`);

    const records = await fetchFromBaserow(tableId);
    const updates = await Promise.all(records.map(createOgImage));

    for (const update of updates) {
        await updateBaserowRow(tableId, update.id, { og_image: update.og_image });
    }

    console.log(`OG_IMAGES UPDATED SUCCESSFULLY for ${tableName}`);
}

async function bootstrapOgImages(tables) {
    for (const tableName of tables) {
        await updateOgImages(tableName);
    }
}

async function fetchDataFromTable(tableName) {
    const tableId = TABLE_IDS[tableName];
    if (!tableId) return null;

    const records = await fetchFromBaserow(tableId);
    return { table: tableName, data: records };
}

export async function fetchTablesFromBaserow() {
    return Promise.all(TABLE_NAMES.map(fetchDataFromTable));
}

const normalizeRecord = (record) => {
    const mergedEvents = mergeEvents(record);
    return {
        id: record.id,
        ...record,
        other_events: mergedEvents
    };
};

if (process.env.NODE_ENV === 'production') {
    bootstrapOgImages(['dancing_agenda', 'social_agenda']);
    console.log(`${process.env.NODE_ENV} Environment`);
}

const tables = await fetchTablesFromBaserow();
const getTable = (tableName) => tables.find((t) => t.table === tableName)?.data || [];

export const latinEvents = getTable('dancing_agenda').map(normalizeRecord);
export const socialEvents = getTable('social_agenda').map(normalizeRecord);
export const reviews = getTable('reviews').map(normalizeRecord);
export const vocabulary = getTable('vocabulary').map(normalizeRecord);
export const bio = getTable('bio').map(normalizeRecord);
export const settings = getTable('settings').map(normalizeRecord);
export const properties = getTable('housing').map(normalizeRecord);

const servicesRaw = getTable('services')
    .map(normalizeRecord)
    .filter(s => s.published);

export const services = await Promise.all(servicesRaw.map(async (s) => ({
    ...s,
    cover: await getImage({ src: s.covers[0].url, format: 'webp', width: 1080, height: 1080 })
})));