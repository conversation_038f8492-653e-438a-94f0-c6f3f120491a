import Airtable from "airtable";
import { getImage } from 'astro:assets';
import { uploadImageAndGetUrl } from 'src/connectors/pulpo';
import { createSocialImageWithText } from 'src/utils/imagesUtils';

const AIRTABLE_BASE_ID = import.meta.env.AIRTABLE_BASE_ID;
const AIRTABLE_ACCESS_TOKEN = import.meta.env.AIRTABLE_ACCESS_TOKEN;

const base = new Airtable({ apiKey: AIRTABLE_ACCESS_TOKEN }).base(AIRTABLE_BASE_ID);

const TABLE_NAMES = [
    "social_agenda",
    "dancing_agenda",
    "reviews",
    "vocabulary",
    "bio",
    "services",
    "housing",
    "settings",
];

function mergeEvents(record) {
    const otherEvents = record.get('other_events') || [];
    const fixedEvents = record.get('fixed_events') || [];
    return [...otherEvents, ...fixedEvents];
}

async function createOgImage(record) {
    const mergedEvents = mergeEvents(record);
    const eventCount = mergedEvents.length;
    const textComputed = eventCount > 1 ? `${eventCount} eventos` : eventCount === 1 ? "1 evento" : "No hay eventos";

    const imageBuffer = await createSocialImageWithText(textComputed);
    const imageUrl = await uploadImageAndGetUrl(imageBuffer);

    return {
        id: record.id,
        fields: {
            'og_image': [{ url: imageUrl, filename: `event-${record.id}.png` }]
        }
    };
}

async function updateOgImages(baseName) {
    const records = await base(baseName).select({ fields: ['other_events', 'fixed_events'] }).firstPage();
    const updates = await Promise.all(records.map(createOgImage));

    for (let i = 0; i < updates.length; i += 10) {
        await base(baseName).update(updates.slice(i, i + 10));
    }

    console.log(`OG_IMAGES UPDATED SUCCESSFULLY for ${baseName}`);
}

async function bootstrapOgImages(bases) {
    for (const baseName of bases) {
        await updateOgImages(baseName);
    }
}

async function fetchDataFromTable(table) {
    const tableSanitized = table.toLowerCase();
    if (!TABLE_NAMES.includes(tableSanitized)) return null;

    const records = await base(table).select({ view: "Grid view" }).all();
    return { table: tableSanitized, data: records };
}

export async function fetchTablesFromAirdata() {
    return Promise.all(TABLE_NAMES.map(fetchDataFromTable));
}

const normalizeRecord = ({ id, fields }) => {
    const mergedEvents = mergeEvents({ get: (field) => fields[field] });
    return {
        id,
        ...fields,
        other_events: mergedEvents  // Sostituisce other_events con gli eventi uniti
    };
};

if (process.env.NODE_ENV === 'production') {
    bootstrapOgImages(['dancing_agenda', 'social_agenda']);
    console.log(`${process.env.NODE_ENV} Environment`);
}

const tables = await fetchTablesFromAirdata();
const getTable = (tableName) => tables.find((t) => t.table === tableName)?.data || [];

export const latinEvents = getTable('dancing_agenda').map(normalizeRecord);
export const socialEvents = getTable('social_agenda').map(normalizeRecord);
export const reviews = getTable('reviews').map(normalizeRecord);
export const vocabulary = getTable('vocabulary').map(normalizeRecord);
export const bio = getTable('bio').map(normalizeRecord);
export const settings = getTable('settings').map(normalizeRecord);
export const properties = getTable('housing').map(normalizeRecord);

const servicesRaw = getTable('services').map(normalizeRecord).filter(s => s.published);
export const services = await Promise.all(servicesRaw.map(async (s) => ({
    ...s,
    cover: await getImage({ src: s.covers[0].url, format: 'webp', width: 1080, height: 1080 })
})));