---
import { Icon } from 'astro-icon/components';

export interface Props {
  title: string;
  items: [];
}

const { items, title } = Astro.props;
---

<div class="relative px-4 md:px-6 py-12 md:py-16 lg:py-20 text-default max-w-7xl mx-auto">
  <div class="mb-8 md:mx-auto md:mb-12 text-center max-w-3xl">
    <h2 class="font-bold leading-tighter tracking-tighter font-heading text-heading text-3xl md:text-4xl">
      {title}
    </h2>
  </div>
  <div class="grid sm:gap-y-8 lg:grid-cols-3 sm:grid-cols-2 gap-4 md:gap-6">
    {
      items.map((i) => (
        <a href={i?.href}>
          <div class="flex flex-row pl-4 py-2 gap-2 items-center rounded-lg overflow-hidden backdrop-blur bg-white border border-[#ffffff29] dark:bg-slate-900 dark:shadow-[0_4px_30px_rgba(0,0,0,0.1)] shadow-[0_4px_30px_rgba(0,0,0,0.1)]">
            <span class="flex-shrink-0 inline-flex mx-3 item-center justify-center leadi rounded-full bg-primary text-white">
              <Icon name={i?.icon} width={30} height={30} class="inline-block m-2" />
            </span>
            <div class="flex-1 p-2">
              <p class="dark:text-gray-100">{i?.title}</p>
            </div>
          </div>
        </a>
      ))
    }
  </div>
</div>
