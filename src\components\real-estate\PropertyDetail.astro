---
import PropertyAmenities from './property/PropertyAmenities.astro';
import PropertyFeatures from './property/PropertyFeatures.astro';
import PropertyGallery from './property/PropertyGallery.astro';
import PropertyHeader from './property/PropertyHeader.astro';
import PropertyHouseRules from './property/PropertyHouseRules.astro';
import PropertyLocation from './property/PropertyLocation.astro';
import PropertySidebar from './property/PropertySidebar.astro';
import { getLangFromUrl, useTranslations } from '~/i18n/utils';

// i18n setup
const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);

interface Props {
  id: string;
  name: string;
  title_en: string;
  description_es: string;
  price: number;
  address: string;
  images: {
    id: string;
    width: number;
    height: number;
    url: string;
    filename: string;
    size: number;
    type: string;
    thumbnails: string;
  }[];
  bedrooms: number;
  bathrooms: number;
  area: number;
  rating: number;
  type: string;
  mode: string;
  iframe_map: string;
  features: string[];
  description_en: string;
  title_es: string;
  home_features_en: string;
  room_features_en: string;
  housemates_en: string;
  smoking_allowed?: boolean;
  pets_allowed?: boolean;
  couples_allowed?: boolean;
  contact_full_name: Array<string>;
  contact_email: Array<string>;
  contact_wa_number?: Array<string>;
  contact_phone_number?: Array<string>;
  contact_category?: Array<string>;
  contact_notes?: Array<string>;
  contacts?: string[];
  contact_fullname?: string[];
}

const {
  id,
  name,
  title_en,
  description_es,
  price,
  address,
  images,
  bedrooms,
  bathrooms,
  area,
  rating,
  type,
  mode,
  iframe_map,
  features,
  description_en,
  title_es,
  home_features_en,
  room_features_en,
  housemates_en,
  smoking_allowed = false,
  pets_allowed = false,
  couples_allowed = false,
  contact_full_name = [],
  contact_email = [],
  contact_phone_number = [],
  contact_wa_number = [],
  contact_category = [],
  contact_notes = [],
  contact_fullname = [],
} = Astro.props;

// Seleziona la descrizione in base alla lingua (utilizzando description_en come fallback)
const description = lang === 'en' ? description_en : description_es;
const title = lang === 'en' ? title_en : title_es;

// Get contact info (with fallbacks)
const contactInfo = {
  fullname: contact_fullname?.[0] || '',
  email: contact_email?.[0] || '',
  whatsapp: contact_wa_number?.[0] || '',
  phone: contact_phone_number?.[0] || '',
};
---

<article class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 dark:bg-gray-900">
  <div class="space-y-8">
    <!-- Header -->
    <PropertyHeader title={title} price={price} address={address} lang={lang} />

    <!-- Main Content with Sidebar -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
      <!-- Main Content -->
      <div class="lg:col-span-2 space-y-8">
        <!-- Image Gallery -->
        <PropertyGallery images={images} t={t} />

        <!-- Features -->
        <PropertyFeatures bedrooms={bedrooms} bathrooms={bathrooms} area={area} t={t} />

        <!-- House Rules -->
        <PropertyHouseRules
          smoking_allowed={smoking_allowed}
          pets_allowed={pets_allowed}
          couples_allowed={couples_allowed}
          t={t}
        />

        <!-- Description -->
        {
          description && (
            <div class="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm">
              <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">{t('description')}</h2>
              <p class="text-gray-700 dark:text-gray-300 leading-relaxed whitespace-pre-line">{description}</p>
            </div>
          )
        }

        <!-- Amenities -->
        <PropertyAmenities features={features} t={t} />

        <!-- Map -->
        {iframe_map && <PropertyLocation iframe_map={iframe_map} t={t} />}
      </div>

      <!-- Sidebar with Contact Form and Quick Actions -->
      <PropertySidebar title={title} address={address} contactInfo={contactInfo} showQuickActions />
    </div>
  </div>
</article>
