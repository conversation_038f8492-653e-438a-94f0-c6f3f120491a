---
import Steps from '~/components/widgets/Steps.astro';
import { getLangFromUrl, useTranslations } from '~/i18n/utils';

const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);
---

<Steps
  id="past_events_cta"
  title={t('dancing.pastEvents.title')}
  items={t('dancing.pastEvents.items')}
  image={{
    src: '~/assets/images/ctas/socialization_media_section.webp',
    alt: 'Photos and Videos from Past Social Events',
  }}
/>
