---
import { Icon } from 'astro-icon/components';
import MiniBanner from '~/components/widgets/MiniBanner.astro';
import { socialLinks } from '~/data/socialLinks';

export interface Props {
  ctaText: string;
  section?: string;
}
const DEFAULT_SECTION = 'livegc';

const { ctaText, section = DEFAULT_SECTION } = Astro.props;

function getSections(links: typeof socialLinks) {
  const sections: string[] = [];
  links.forEach((link) => {
    link.sections.forEach((section) => {
      if (!sections.includes(section)) {
        sections.push(section);
      }
    });
  });
  return sections;
}

const allowedSections = getSections(socialLinks);

const sectionComputed = allowedSections.includes(section) ? section : DEFAULT_SECTION;

const platformSocialLinks = socialLinks.filter((link) => link.sections.includes(sectionComputed));
---

<MiniBanner>
  <div class="py-4">
    <h2 class="text-3xl font-semibold text-center mb-6 animate-bounce ease-in">👇 {ctaText}</h2>

    <div class="hidden">
      <div class="bg-green-500"></div>
      <div class="bg-red-500"></div>
      <div class="bg-yellow-500"></div>
      <div class="bg-blue-500"></div>
    </div>

    <div class="flex flex-wrap justify-center gap-2">
      {
        platformSocialLinks.map((social) => {
          return (
            <a
              href={social.href}
              target="_blank"
              class={`bg-${social.color}-500 hover:scale-150 hover:ease-in duration-300 hover:animate-pulse p-2 font-semibold text-white inline-flex items-center space-x-2 rounded mx-2`}
            >
              <Icon name={social.icon} class="w-6 h-6" />
            </a>
          );
        })
      }
    </div>
  </div>
</MiniBanner>
