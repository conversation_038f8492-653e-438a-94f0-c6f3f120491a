---
import { Icon } from 'astro-icon/components';
import { getLangFromUrl, useTranslations } from '~/i18n/utils';
import FloatingWhatsappBtn from '../common/FloatingWhatsappBtn.astro';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';

dayjs.extend(utc);
dayjs.extend(timezone);

const tz = 'Atlantic/Canary';
const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);

interface Props {
  datetimeStart: string;
  datetimeEnd: string;
  locationName: string[];
  locationAddress: string[];
  locationLinkMap: string[];
  cost: string;
  costDetails?: string;
  childrenFriendly?: boolean;
  contact?: string;
  contactType?: string;
}

const {
  datetimeStart,
  datetimeEnd,
  locationName,
  locationAddress,
  locationLinkMap,
  cost,
  costDetails,
  contact,
  contactType,
  childrenFriendly,
} = Astro.props;

const formatDateTime = (start: string, end: string) => {
  if (!start || !end) return null;
  const startDate = dayjs(start).tz(tz);
  const endTime = dayjs(end).tz(tz).format('HH:mm');
  return `${startDate.format('DD MMM YYYY')}, ${startDate.format('HH:mm')} ~ ${endTime}`;
};

const dateTimeFormatted = formatDateTime(datetimeStart, datetimeEnd);
const costClass = cost ? 'text-yellow-600 dark:text-yellow-400' : 'text-green-600 dark:text-green-400';
const costText = cost ? `${cost} euro` : 'Free Event';
---

<p class="flex flex-col">
  {
    dateTimeFormatted && (
      <span class="flex items-center">
        <Icon name="tabler:calendar" width={20} height={20} class="inline-block -mt-0.5 mr-2" />
        <small>{dateTimeFormatted}</small>
      </span>
    )
  }
  {
    locationName && (
      <span class="flex items-center">
        <Icon name="tabler:building-skyscraper" width={20} height={20} class="inline-block -mt-0.5 mr-2" />
        <strong>{locationName}</strong>
      </span>
    )
  }
  {
    locationAddress && (
      <small class="flex items-center text-xs">
        <Icon name="tabler:map-pin" width={20} height={20} class="inline-block -mt-0.5 mr-2" />
        {locationLinkMap ? (
          <a href={locationLinkMap} target="_blank" rel="noopener noreferrer">
            {locationAddress}
          </a>
        ) : (
          locationAddress
        )}
      </small>
    )
  }
  <span class="flex items-center">
    <Icon name="tabler:coin-euro" width={20} height={20} class="inline-block -mt-0.5 mr-2" />
    <span>
      <small class={`${costClass} font-bold mr-2`}>{costText}</small>
      <small>{costDetails}</small>
    </span>
  </span>
  {
    childrenFriendly && (
      <span class="flex items-center">
        <Icon name="tabler:horse-toy" width={20} height={20} class="inline-block -mt-0.5 mr-2" />
        <small>{t('childrenFriendly')}</small>
      </span>
    )
  }

  <!-- Whatsapp Button -->
  <!-- {
    contact && contactType === 'whatsapp' && (
      <span class="flex items-center text-xs">
        <Icon name="tabler:brand-whatsapp" width={20} height={20} class="inline-block -mt-0.5 mr-2" />
        <a href={`https://wa.me/${import.meta.env.WHATSAPP_SUPPORT}`} target="_blank">
          {t('contact')}
        </a>
      </span>
    )
  } -->
</p>

{contact && contactType === 'whatsapp' && <FloatingWhatsappBtn number={contact} />}
