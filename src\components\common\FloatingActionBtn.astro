---
// https://tailwindcomponents.com/component/floating-button
import { Icon } from 'astro-icon/components';
import { getLangFromUrl } from '~/i18n/utils';
const lang = getLangFromUrl(Astro.url);
---

<!-- component -->
<div class="group fixed bottom-0 right-0 p-2 flex items-end justify-end w-24 h-24" style="z-index: 999;">
  <!-- main -->
  <div
    class="text-white shadow-xl flex items-center justify-center p-3 rounded-full bg-gradient-to-r bg-primary z-100 absolute"
  >
    <Icon name="tabler:asterisk" width={24} height={24} class="inline-block" />
  </div>

  <!-- sub left -->
  <div
    id="refresh-btn"
    class="cursor-pointer absolute rounded-full transition-all duration-[0.2s] ease-out scale-y-0 group-hover:scale-y-100 group-hover:-translate-x-16 flex p-2 hover:p-3 bg-blue-500 hover:bg-blue-600 text-white"
  >
    <Icon name="tabler:refresh" width={24} height={24} class="inline-block" />
  </div>
  <!-- sub top -->
  <a
    href={`/${lang === 'en' ? '' : lang}`}
    class="absolute rounded-full transition-all duration-[0.2s] ease-out scale-x-0 group-hover:scale-x-100 group-hover:-translate-y-16 flex p-2 hover:p-3 bg-red-500 scale-100 hover:bg-red-600 text-white"
  >
    <Icon name="tabler:home-2" width={24} height={24} class="inline-block" />
  </a>
  <!-- sub middle -->
  <a
    href={`https://wa.me/${import.meta.env.WHATSAPP_SUPPORT}`}
    target="_blank"
    class="absolute rounded-full transition-all duration-[0.2s] ease-out scale-x-0 group-hover:scale-x-100 group-hover:-translate-y-14 group-hover:-translate-x-14 flex p-2 hover:p-3 bg-green-400 hover:bg-green-500 text-white"
  >
    <Icon name="tabler:brand-whatsapp" width={24} height={24} class="inline-block" />
  </a>
</div>

<script>
  const button = document.getElementById('refresh-btn');
  if (button) button.addEventListener('click', () => location.reload(true));
</script>
