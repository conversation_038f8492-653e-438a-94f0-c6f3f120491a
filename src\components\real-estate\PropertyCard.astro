---
import { Icon } from 'astro-icon/components';
import { getLangFromUrl, useTranslations, useTranslatedPath } from '~/i18n/utils';
import { Image } from 'astro:assets';

// Definizione del tipo Property
interface Image {
  url: string;
  alt?: string;
  id?: string;
  width?: number;
  height?: number;
  filename?: string;
  size?: number;
  type?: string;
  thumbnails?: any;
}

interface Property {
  id: number | string;
  price: number;
  address: string;
  images: Image[];
  cover?: Image[];
  bedrooms: number;
  bathrooms: number;
  area: number;
  rating: number;
  type: string;
  features: string[];
  mode?: string;
  name?: string;
  title_en?: string;
  title_es?: string;
  description_en?: string;
  description_es?: string;
}

const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);
const translatePath = useTranslatedPath(lang);

interface Props {
  property: Property;
}

const { property } = Astro.props;

// Debug property data
// console.log('PropertyCard - Property Data:', {
//   name: property.name,
//   hasCover: property.cover && property.cover.length > 0,
//   coverUrl: property.cover && property.cover.length > 0 ? property.cover[0].url : 'No cover',
//   hasImages: property.images && property.images.length > 0,
//   imageUrl: property.images && property.images.length > 0 ? property.images[0].url : 'No images',
// });

const path = `${translatePath('real-estate')}/${property.name}`;
const defaultImage = '/images/property-placeholder.jpg';
const formattedPrice = new Intl.NumberFormat(lang, { style: 'currency', currency: 'EUR' }).format(property.price || 0);

// Get description and title based on language
const title = lang === 'en' ? property.title_en : property.title_es;

// Mode badge colors
const modeStyles = {
  rent: 'border-2 border-blue-400 text-blue-500 bg-white/80 dark:bg-gray-900/80',
  sale: 'border-2 border-rose-400 text-rose-500 bg-white/80 dark:bg-gray-900/80',
};

// Extract features
const features = [
  { name: 'bedrooms', icon: 'tabler:bed', value: property.bedrooms || 0 },
  { name: 'bathrooms', icon: 'tabler:bath', value: property.bathrooms || 0 },
  { name: 'area', icon: 'tabler:square-rotated', value: `${property.area || 0} m²` },
];
---

<a href={path} class="block w-full max-w-6xl mx-auto transition-transform duration-300 hover:-translate-y-1">
  <article
    class="bg-white dark:bg-gray-900 rounded-xl overflow-hidden shadow-md hover:shadow-xl group border border-gray-100 dark:border-gray-700"
  >
    <div class="relative w-full aspect-[4/3] overflow-hidden">
      {
        (
          <Image
            src={
              property.cover && property.cover.length > 0
                ? property.cover[0].url
                : property.images && property.images.length > 0
                  ? property.images[0].url
                  : defaultImage
            }
            class="absolute inset-0 w-full h-full object-cover transition-transform duration-500 group-hover:scale-105"
            width={800}
            height={600}
            widths={[400, 800, 1200]}
            sizes="(max-width: 400px) 400px, (max-width: 800px) 800px, 1200px"
            alt={property.name || 'Property image'}
            loading="lazy"
            decoding="async"
          />
        )
      }

      <!-- Price Badge -->
      <div class="absolute top-4 right-4">
        <span
          class="px-3 py-1.5 text-sm font-semibold bg-white/90 dark:bg-gray-900/90 text-emerald-600 dark:text-emerald-400 rounded-lg backdrop-blur-sm"
        >
          {formattedPrice}
        </span>
      </div>

      <!-- Mode Badge -->
      {
        property.mode && (
          <div class="absolute top-4 left-4">
            <span
              class={`
              px-3 py-1.5 text-xs font-semibold uppercase tracking-wider backdrop-blur-sm
              ${property.mode === 'rent' ? modeStyles.rent : modeStyles.sale}
              rounded-xl
            `}
            >
              {property.mode === 'rent' ? t('for_rent' as any) : t('for_sale' as any)}
            </span>
          </div>
        )
      }

      <!-- Images Counter Badge
      {
        property.images && property.images.length > 0 && (
          <div class="absolute bottom-4 left-4">
            <span class="flex items-center gap-1.5 px-2 py-1 text-xs font-medium bg-black/50 text-white rounded-lg backdrop-blur-sm">
              <Icon name="tabler:camera" class="w-4 h-4" />
              <span>{property.images.length}</span>
            </span>
          </div>
        )
      } -->
    </div>

    <div class="p-5">
      <!-- Title -->
      <!-- <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2 line-clamp-1">
        {property.name}
      </h3> -->

      <!-- Location -->
      <div class="flex items-center gap-1.5 text-gray-600 dark:text-gray-300 mb-3">
        <Icon name="tabler:map-pin" class="w-4 h-4" />
        <p class="text-sm line-clamp-1">{property.address}</p>
      </div>

      <!-- Description -->
      <p class="text-gray-600 dark:text-gray-300 text-sm mb-4 line-clamp-1 opacity-60 group-hover:opacity-100">
        {title || t('no_description_available' as any)}
      </p>

      <!-- Stats and Features -->
      <div class="flex justify-between items-center border-t border-gray-100 dark:border-gray-700 pt-4 mt-2">
        <div class="flex items-center gap-1.5">
          <Icon name="tabler:camera" class="w-5 h-5 text-gray-500 dark:text-gray-400" stroke-width={1.5} />
          <span class="text-sm font-medium">
            {property.images ? property.images.length : 0}
          </span>
        </div>

        <div class="flex gap-4">
          {
            features.map((feature) => (
              <div class="flex items-center gap-1.5">
                <Icon name={feature.icon} class="w-5 h-5 text-gray-500 dark:text-gray-400" stroke-width={1.5} />
                <span class="text-sm text-gray-600 dark:text-gray-300">{feature.value}</span>
              </div>
            ))
          }
        </div>
      </div>
    </div>
  </article>
</a>
