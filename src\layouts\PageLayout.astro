---
import Layout from '~/layouts/Layout.astro';
import Header from '~/components/widgets/Header.astro';
import Footer from '~/components/widgets/Footer.astro';

import { headerData, footerData } from '~/navigation';

import type { MetaData } from '~/types';
import DonateNoteButton from '~/components/common/DonateNoteButton.astro';
import Popup from '~/components/widgets/Popup.astro';
import PopupFollowUs from '~/components/ui/PopupFollowUs.astro';

export interface Props {
  metadata?: MetaData;
  noDonate?: boolean;
}

const { metadata, noDonate } = Astro.props;

const allowedRoutes = ['/', '/dancing', '/es/baile', '/socialization', '/es/socializacion'];
const exactRoutes = ['/es'];

// const popupImageAltText = 'Follow us on Socials';
const popupImageAltText = 'Retro Party Night!';
const popupImageUrl = 'https://retro-party-livegc.netlify.app/_astro/og-flyer.D7orqM2w_Z2sNvqY.webp';
const popupLinkUrl = 'https://retro-party-livegc.netlify.app/en/';
// const popupCountdownText = 'countdown.text';
const popupPosition = 'bottom-right';
// const countdownEndDate = '2024/10/30 23:59:59';
const hideDays = 3;
---

<Layout metadata={metadata}>
  <slot name="header">
    <Header {...headerData} isSticky showToggleTheme />
  </slot>
  <main>
    <slot />
  </main>

  <!-- <DonateNoteButton link={import.meta.env.KO_FI_LINK} targetBlank /> -->
  {noDonate ? '' : <DonateNoteButton link={import.meta.env.KO_FI_LINK} targetBlank />}

  <!-- <Popup
    allowedRoutes={allowedRoutes}
    exactRoutes={exactRoutes}
    hideDays={hideDays}
    position={popupPosition}
    countdownEndDate={countdownEndDate}
    countdownText={popupCountdownText}
    imageUrl={popupImageUrl}
    altText={popupImageAltText}
    linkUrl={popupLinkUrl}
    fullscreen={false}
  > -->

  <Popup
    allowedRoutes={allowedRoutes}
    exactRoutes={exactRoutes}
    hideDays={hideDays}
    position={popupPosition}
    imageUrl={popupImageUrl}
    linkUrl={popupLinkUrl}
    fullscreen={false}
    openInNewTab
  >
    <!-- <PopupFollowUs /> -->
  </Popup>

  <slot name="footer">
    <Footer {...footerData} />
  </slot>
</Layout>
