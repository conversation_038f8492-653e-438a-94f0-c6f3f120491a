---
import { APP_BLOG } from '~/utils/config';
import { defaultLang } from '~/i18n/ui';
import type { Post } from '~/types';
import Image from '~/components/common/Image.astro';
import { findImage } from '~/utils/images';
import { getPermalink } from '~/utils/permalinks';
import { getLangFromUrl, useTranslations } from '~/i18n/utils';

interface Props {
  post: Post;
}

const { post } = Astro.props;

const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);
const isDefaultLang = lang === defaultLang;
const prefix = isDefaultLang ? '' : `${lang}/`;

const permalinkComputed = getPermalink(`${prefix}${post.permalink}`, 'post');
const image = await findImage(post.image);

const formattedDate = post.publishDate
  ? new Date(post.publishDate).toLocaleDateString(lang, {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    })
  : null;
---

<article
  class="mb-6 transition-all transform hover:scale-[1.02] hover:-translate-y-1 shadow-md hover:shadow-xl duration-300 ease-in-out p-4 flex flex-col justify-between group h-full bg-white dark:bg-slate-900 overflow-hidden"
>
  <div class="relative md:h-64 bg-gray-400 dark:bg-slate-700 rounded shadow-lg mb-6">
    {
      image && (
        <a href={permalinkComputed}>
          <Image
            src={image}
            class="w-full md:h-full shadow-lg bg-gray-400 dark:bg-slate-700"
            widths={[400, 900]}
            width={400}
            sizes="(max-width: 900px) 400px, 900px"
            alt={post.title}
            aspectRatio="16:9"
            layout="cover"
            loading="lazy"
            decoding="async"
          />
        </a>
      )
    }
  </div>

  {/* Title */}
  <h3 class="mb-3 text-xl font-bold leading-tight sm:text-2xl font-heading text-justify">
    {
      APP_BLOG?.post?.isEnabled ? (
        <a
          href={permalinkComputed}
          class="text-gray-900 dark:text-white hover:text-primary-600 dark:hover:text-primary-400 transition duration-200"
        >
          {post.title}
        </a>
      ) : (
        post.title
      )
    }
  </h3>

  {/* Category Tag - if available */}
  <!-- {
    post.category && (
      <div class="mb-3">
        <span class="inline-block px-3 py-1 text-xs font-semibold text-primary-600 dark:text-primary-400 bg-primary-100 dark:bg-primary-900 rounded-full">
          {post.category}
        </span>
      </div>
    )
  } -->

  <a
    href={permalinkComputed}
    class="inline-flex items-center text-sm font-medium text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300"
  >
    <p class="mb-4 text-muted line-clamp-3">{post.excerpt}</p>
  </a>

  <div class="flex items-center justify-between mt-auto pt-4">
    <!-- {formattedDate && <span class="text-sm text-gray-500 dark:text-gray-400">{formattedDate}</span>} -->
    {
      APP_BLOG?.post?.isEnabled && (
        <a
          href={permalinkComputed}
          class="inline-flex items-center text-sm font-medium text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300"
        >
          {t('readMore')}
          <svg class="w-4 h-4 ml-1" viewBox="0 0 20 20" fill="currentColor">
            <path
              fill-rule="evenodd"
              d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z"
              clip-rule="evenodd"
            />
          </svg>
        </a>
      )
    }
  </div>
</article>
