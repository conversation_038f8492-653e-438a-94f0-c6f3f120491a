---
import type { InferGetStaticPropsType, GetStaticPaths } from 'astro';

import Layout from '~/layouts/PageLayout.astro';
import BlogList from '~/components/blog/List.astro';
import Headline from '~/components/blog/Headline.astro';
import Pagination from '~/components/blog/Pagination.astro';
import PostTags from '~/components/blog/Tags.astro';

import {
  blogListRobots,
  getStaticPathsBlogList,
  filterPostsByLocale,
  fetchPosts,
  getAllCategoriesFromPosts,
  getAllTagsFromPosts,
} from '~/utils/blog';

import { getLangFromUrl, useTranslations } from '~/i18n/utils';

const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);

export const prerender = true;

export const getStaticPaths = (async ({ paginate }) => {
  return await getStaticPathsBlogList({ paginate });
}) satisfies GetStaticPaths;

type Props = InferGetStaticPropsType<typeof getStaticPaths>;

const { page } = Astro.props as Props;
const currentPage = page.currentPage ?? 1;

let posts = await fetchPosts();
posts = filterPostsByLocale(posts, lang);

const allCategories = getAllCategoriesFromPosts(posts);
const allTags = getAllTagsFromPosts(posts);

const metadata = {
  title: `Contents ${currentPage > 1 ? ` — Page ${currentPage}` : ''}`,
  robots: {
    index: blogListRobots?.index && currentPage === 1,
    follow: blogListRobots?.follow,
  },
  openGraph: {
    type: 'blog',
  },
};
---

<Layout metadata={metadata}>
  <section class="px-6 sm:px-6 py-12 sm:py-16 lg:py-20 mx-auto max-w-4xl">
    <Headline subtitle={t('blog.headline.subtitle')}> {t('blog.headline.title')} </Headline>
    <BlogList posts={filterPostsByLocale(page.data, lang)} />
    <Pagination prevUrl={page.url.prev} nextUrl={page.url.next} />

    <PostTags tags={allCategories} class="mb-2" title={t('searchByCategories')} isCategory />
    <PostTags tags={allTags} title={t('searchByTags')} />
  </section>
</Layout>
