---
import ImgHostedWithSlideshow from '~/components/common/ImgHostedWithSlideshow.astro';


interface Props {
  images: {
    id: string;
    width: number;
    height: number;
    url: string;
    filename: string;
    size: number;
    type: string;
    thumbnails: string;
  }[];
  t: any; // Translation function
}

const { images, t } = Astro.props;
---

<div class="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm space-y-4">
  <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">{t('gallery')}</h2>

  <!-- Main large image -->
  {
    images.length > 0 && (
      <div class="w-full aspect-video overflow-hidden rounded-lg">
        <ImgHostedWithSlideshow
          flyerUrl={images[0].url}
          index={0}
          length={images.length}
          prefix="pic"
          className="w-full h-full object-cover"
        />
      </div>
    )
  }

  <!-- Thumbnails -->
  {
    (
      <div class="grid grid-cols-5 gap-2 mt-4">
        {images.map((image, index) => (
          <div class="aspect-square overflow-hidden rounded-md">
            <ImgHostedWithSlideshow
              flyerUrl={image.url}
              index={index}
              length={images.length}
              prefix="pic"
              className="w-full h-full object-cover cursor-pointer hover:opacity-80 transition-opacity"
            />
          </div>
        ))}
      </div>
    )
  }
</div>
