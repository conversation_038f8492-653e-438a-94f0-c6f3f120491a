---
import Headline from '~/components/ui/Headline.astro';
import WidgetWrapper from '~/components/ui/WidgetWrapper.astro';
import CTA from '~/components/ui/CTA.astro';
import { Image } from 'astro:assets';
import type { Testimonials } from '~/types';

const {
  title = '',
  subtitle = '',
  tagline = '',
  testimonials = [],
  callToAction,
  id,
  isDark = false,
  classes = {},
  bg = await Astro.slots.render('bg'),
} = Astro.props as Testimonials;
---

<WidgetWrapper id={id} isDark={isDark} containerClass={`max-w-7xl mx-auto ${classes?.container ?? ''}`} bg={bg}>
  <Headline title={title} subtitle={subtitle} tagline={tagline} />

  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
    {
      testimonials.map(({ fullname, picture, review, url_review, provider }) => (
        <div class="flex flex-col h-full p-6 bg-white dark:bg-slate-900 rounded-lg shadow-lg dark:shadow-none dark:border dark:border-slate-700">
          {review && (
            <div class="mb-6">
              <blockquote class="text-lg italic text-gray-700 dark:text-gray-300">"{review}"</blockquote>
            </div>
          )}

          <div class="mt-auto flex items-center">
            {picture && picture[0] && (
              <div class="flex-shrink-0">
                <Image
                  class="h-12 w-12 rounded-full object-cover"
                  width={48}
                  height={48}
                  src={picture[0].url}
                  alt={fullname}
                />
              </div>
            )}
            <div class="ml-4">
              <div class="font-medium text-gray-900 dark:text-white">
                {fullname && url_review ? (
                  <a href={url_review} target="_blank" rel="noopener noreferrer" class="hover:underline">
                    {fullname}
                  </a>
                ) : (
                  fullname
                )}
              </div>
              {provider && <div class="text-sm text-gray-500 dark:text-gray-400">{provider}</div>}
            </div>
          </div>
        </div>
      ))
    }
  </div>

  {
    callToAction && (
      <div class="mt-12 text-center">
        <CTA callToAction={callToAction} />
      </div>
    )
  }
</WidgetWrapper>
