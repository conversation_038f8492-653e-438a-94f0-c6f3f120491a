---
import { getLangFromUrl, useTranslations, useTranslatedPath } from '~/i18n/utils';
import type { ImageMetadata } from 'astro';

interface Props {
  name: string;
  slug?: string;
  emoji?: string;
  cover?: ImageMetadata;
  shortDescription?: string;
}

const { name, emoji = '', cover, slug = '', shortDescription = '' } = Astro.props;

const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);
const translatePath = useTranslatedPath(lang);

const serviceUrl = `${translatePath('/services')}/${slug}`;
---

<a href={slug ? serviceUrl : '#'} class="block group" aria-label={`View details for ${name}`}>
  <article
    class="h-full overflow-hidden rounded-lg bg-white dark:bg-gray-800 shadow-lg transition-all duration-300 hover:shadow-xl dark:shadow-gray-700/30"
  >
    <div class="flex flex-col lg:flex-row h-full">
      {
        cover && (
          <div class="relative w-full lg:w-2/5">
            <img
              src={cover.src}
              alt={`${name} service illustration`}
              width={400}
              height={400}
              loading="eager"
              decoding="async"
              class="w-full h-48 lg:h-full object-cover transition-transform duration-300 group-hover:scale-105"
            />
          </div>
        )
      }

      <div class="flex flex-col justify-between p-4 md:p-6 lg:w-3/5 w-full">
        <div class="space-y-2 px-2 md:px-0">
          <h3
            class="text-xl font-semibold text-gray-900 dark:text-white group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors duration-300"
          >
            {name}
            {
              emoji && (
                <span class="ml-2" role="img" aria-label="Service emoji">
                  {emoji}
                </span>
              )
            }
          </h3>

          {
            shortDescription && (
              <p class="text-xs overflow-hidden line-clamp-3 text-gray-600 dark:text-gray-300">{shortDescription}</p>
            )
          }
        </div>

        <button
          class="mt-4 mx-2 md:mx-0 inline-flex items-center px-3 py-2 text-xs font-bold text-white uppercase bg-gray-800 rounded dark:bg-gray-700 hover:bg-gray-700 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-primary-500 transition-colors duration-300"
        >
          <span class="mr-2">{t('readMore')}</span>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-4 w-4 transition-transform duration-300 group-hover:translate-x-2"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
            aria-hidden="true"
          >
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
          </svg>
        </button>
      </div>
    </div>
  </article>
</a>
