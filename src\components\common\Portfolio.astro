---
import CardWithTextLayer from './CardWithTextLayer.astro';

export interface Props {
  title?: string;
  subtitle?: string;
  images?: Array<{ src: string; alt: string }>;
}

const { title, subtitle, images } = Astro.props;
---

<section>
  <div class="container px-6 py-10 mx-auto">
    {
      title && (
        <h1 class="text-2xl font-semibold text-center text-gray-800 capitalize lg:text-3xl dark:text-white">{title}</h1>
      )
    }

    <div class="grid grid-cols-1 gap-8 mt-8 xl:mt-12 xl:gap-12 lg:grid-cols-3">
      {!!images?.length && images?.map(({ src, alt }) => <CardWithTextLayer title={alt} imageSrc={src} />)}
    </div>
  </div>
</section>
