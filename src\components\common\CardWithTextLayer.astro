---
export interface Props {
  title?: string;
  subtitle?: string;
  imageSrc?: string;
}

const { title, subtitle, imageSrc } = Astro.props;
---

<div class="flex items-end overflow-hidden bg-cover rounded-lg h-96" style=`background-image:url(${imageSrc})`>
  <div class="w-full px-8 py-4 overflow-hidden rounded-b-lg backdrop-blur-sm bg-white/60 dark:bg-gray-800/60">
    {title && <h2 class="mt-4 text-xl font-semibold text-gray-800 capitalize dark:text-white">{title}</h2>}
    {subtitle && <p class="mt-2 text-lg tracking-wider text-blue-500 uppercase dark:text-blue-400">{subtitle}</p>}
  </div>
</div>
