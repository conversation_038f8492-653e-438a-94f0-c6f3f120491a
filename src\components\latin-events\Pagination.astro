---
import { Icon } from 'astro-icon/components';
import { getPermalink } from '~/utils/permalinks';

export interface Props {
  prevUrl: string;
  nextUrl: string;
  prevText?: string;
  nextText?: string;
}

const { prevUrl, nextUrl, prevText = 'Newer posts', nextText = 'Older posts' } = Astro.props;
---

{
  (prevUrl || nextUrl) && (
    <div class="container flex">
      <div class="flex flex-row mx-auto container justify-between">
        <a href={getPermalink(prevUrl)} class={`btn btn-ghost md:px-3 px-3 mr-2 ${!prevUrl ? 'invisible' : ''}`}>
          <div class="flex flex-row align-middle">
            <Icon name="tabler:chevron-left" class="w-6 h-6" />
            <p class="ml-2">{prevText}</p>
          </div>
        </a>
        <a href={getPermalink(nextUrl)} class={`btn btn-ghost md:px-3 px-3 ${!nextUrl ? 'invisible' : ''}`}>
          <div class="flex flex-row align-middle">
            <span class="mr-2">{nextText}</span>
            <Icon name="tabler:chevron-right" class="w-6 h-6" />
          </div>
        </a>
      </div>
    </div>
  )
}
