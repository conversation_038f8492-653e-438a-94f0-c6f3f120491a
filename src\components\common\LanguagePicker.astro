---
import { Icon } from 'astro-icon/components';
import { languages } from '~/i18n/ui';
import { getLangFromUrl, useTranslatedPath } from '~/i18n/utils';
import en from '~/assets/images/locales/en.svg';
import es from '~/assets/images/locales/es.svg';
import it from '~/assets/images/locales/it.svg';
import de from '~/assets/images/locales/en.svg';
import ru from '~/assets/images/locales/ru.svg';

const languageImages = { en, es, it, de, ru };

export interface Props {
  showCurrentFlag?: boolean;
}

const lang = getLangFromUrl(Astro.url);
const translatePath = useTranslatedPath(lang);

const { showCurrentFlag = false } = Astro.props;

let languagesUsed = { ...languages };

if (!showCurrentFlag) {
  delete languagesUsed[lang];
}

const currentPath = Astro.url.pathname;

const processRoute = (route, language) => {
  const pathTranslated = translatePath(route, language);
  if (pathTranslated === '/') return pathTranslated;
  return pathTranslated.endsWith('/') ? pathTranslated.slice(0, -1) : pathTranslated;
};
---

<div class="dropdown inline-block relative">
  <!-- Button -->
  <button
    class="text-muted hover:text-link dark:hover:text-white px-4 py-3 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 rounded-lg text-sm p-2.5 inline-flex items-center"
  >
    <img src={languageImages[lang]?.src} width="16" class="mr-1" alt={lang} />
    <Icon name="tabler:chevron-down" class="w-3.5 h-3.5 ml-0.5 rtl:ml-0 rtl:mr-0.5 hidden md:inline" />
  </button>
  <!-- Dropdown menu -->
  <ul class="dropdown-menu absolute hidden shadow-xl bg-white dark:bg-gray-900 z-20 w-36">
    {
      Object.entries(languagesUsed).map(([lang, label]) => (
        <li>
          <a
            href={processRoute(currentPath, lang)}
            class="flex px-4 py-3 text-sm text-gray-600 capitalize transition-colors duration-300 transform dark:text-gray-300 hover:bg-blue-50 dark:hover:bg-gray-700 dark:hover:text-white"
          >
            <img src={languageImages[lang]?.src} width="16" alt={lang} />
            <span class="ml-2 uppercase">{label}</span>
          </a>
        </li>
      ))
    }
  </ul>
</div>

<style>
  @media (max-width: 768px) {
    .dropdown-menu {
      /* top: 100%; */
      transform: translateY(-100px);
    }
  }
</style>





