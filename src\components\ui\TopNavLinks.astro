---
export interface TopNavLinksProps {
  leftHref: string;
  leftText: string;
  leftVisible: boolean;
  rightHref: string;
  rightText: string;
  rightVisible: boolean;
}

const { leftHref, leftText, leftVisible, rightHref, rightText, rightVisible } = Astro.props;
---

<div class="flex justify-between items-center">
  {
    leftVisible && (
      <a href={leftHref} class="text-primary hover:text-primary-600 transition duration-150 ease-in-out">
        ← {leftText}
      </a>
    )
  }
  {
    rightVisible && (
      <a href={rightHref} class="text-primary hover:text-primary-600 transition duration-150 ease-in-out ml-auto">
        {rightText} →
      </a>
    )
  }
</div>
