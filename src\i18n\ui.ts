import { vocabulary } from '~/connectors/airtable';
import { nestedTranslations } from './nestedTranslations';

export const defaultLang = 'en';
export const showDefaultLang = false;

export const languages = {
    en: 'English',
    es: 'Español',
    // it: 'Italiano',
    // de: 'Deutsch',
    // ru: 'Русский',
    // fr: 'Français',
} as const;

export const locales = Object.keys(languages) as (keyof typeof languages)[];

const formatTranslations = (translations: any[]): Record<string, Record<string, string>> => {
    return translations.reduce((acc, item) => {
        locales.forEach(locale => {
            if (!acc[locale]) acc[locale] = {};
            acc[locale][item.key] = item[locale];
        });
        return acc;
    }, {});
};

const computedVocabulary = formatTranslations(vocabulary);

const generateUILocale = (locale: keyof typeof languages) => ({
    ...computedVocabulary[locale],
    ...nestedTranslations[locale],
});

export const ui = Object.fromEntries(
    locales.map(locale => [locale, generateUILocale(locale)])
) as Record<keyof typeof languages, ReturnType<typeof generateUILocale>>;

export const routes: Record<keyof typeof languages, Record<string, string>> = {
    en: {
        'about': 'about',
        'contact': 'contact',
        'dancing': 'dancing',
        'real-estate': 'real-estate',
        'sections': 'sections',
        'privacy': 'privacy',
        'terms': 'terms',
        'agenda': 'agenda',
        "monday": "monday",
        "tuesday": "tuesday",
        "wednesday": "wednesday",
        "thursday": "thursday",
        "friday": "friday",
        "saturday": "saturday",
        "sunday": "sunday",
        "collaborations": "collaborations",
        "socialization": "socialization",
        "schools": "schools",
        "donate": "donate",
        "bio": "bio",
        "services": "services",
        "photographer": "photographer",
        "posts": "posts",
        "sector": "sector",
        "tag": "tag",
        "platform": "platform",
        "where-to-dance-salsa-bachata-kizomba-gran-canaria": "where-to-dance-salsa-bachata-kizomba-gran-canaria",
        "join-our-whatsapp-community": "join-our-whatsapp-community",
        "flyer": "flyer",
    },
    es: {
        'about': 'acerca-de',
        'contact': 'contacto',
        'dancing': 'baile',
        'real-estate': 'inmobiliaria',
        'sections': 'secciones',
        'privacy': 'privacidad',
        'terms': 'terminos',
        'agenda': 'agenda',
        "monday": "lunes",
        "tuesday": "martes",
        "wednesday": "miercoles",
        "thursday": "jueves",
        "friday": "viernes",
        "saturday": "sabado",
        "sunday": "domingo",
        "collaborations": "colaboraciones",
        "socialization": "socializacion",
        "schools": "escuelas",
        "donate": "donar",
        "bio": "bio",
        "services": "servicios",
        "photographer": "fotografo",
        "posts": "posts",
        "sector": "sector",
        "tag": "tag",
        "platform": "plataforma",
        "where-to-dance-salsa-bachata-kizomba-gran-canaria": "donde-bailar-salsa-bachata-kizomba-gran-canaria",
        "join-our-whatsapp-community": "unete-a-nuestra-comunidad-en-whatsapp",
        "flyer": "cartel",
    },
};